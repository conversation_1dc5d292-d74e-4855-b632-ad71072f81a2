﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;

using System.Collections.Generic;
using System.Linq;
using CPHI.WebScraper.ViewModel;
using System.Diagnostics;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.Repository;
using CPHI.Repository;

namespace CPHI.Spark.WebScraper.Jobs
{

   [DisallowConcurrentExecution]
   public class ModixUpdatePricesJob : IJob
   {
      // Enum to define the operation type for vehicle processing function
      private enum VehicleProcessOperation
      {
         Update,
         Verify
      }

      private static readonly ILog logger = LogManager.GetLogger(typeof(ModixUpdatePricesJob));
      private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
      private const string homePageLink = "https://content.uk1.modix.biz";
      private const int seatSiteId = 14;

      private DealerGroupName dealerGroup;

      Dictionary<int, string> brandDictionary;
      Dictionary<int, string> siteCodeDictionary;

      public void Execute() { }
      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         try
         {
            logger.Info("");
            logger.Info("========================= Starting ModixScrapeJob =====================================");

            stopwatch.Start();

            brandDictionary = InitiateBrandDictionary();
            siteCodeDictionary = InitiateSiteCodeDictionary();

            dealerGroup = DealerGroupName.Vindis;

            ////early return if today is public holiday
            //if (CheckIfTodayIsPubHoliday())
            //{
            //   logger.Info("Today is a public holiday.  Returning...");
            //   return;
            //}

            List<ModixPriceUpdate> vehicles = await GetPriceChangesToProcess(dealerGroup);

            if (vehicles.Count == 0)
            {
               return;
            }

            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";
            ScraperMethodsService.ClearDownloadsFolder();

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisModix2", 9228);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

            // Need to do some kind of function here to get these from DB
            // Create a list to hold ModixVehicle objects
            //List<ModixVehicle> testVehicles =
            //[
            //   new ModixVehicle("MW22UBT", 30415, 7, false),
            //   new ModixVehicle("VE74OCL", 35306, 7, false),
            //   new ModixVehicle("CF22XPW", 18999, 1, false),
            //   new ModixVehicle("GJ73VVB", 21999, 9, false),
            //];

            bool loginSuccessful = await LoginAsync();

            if (loginSuccessful)
            {
               logger.Info($"Login OK, continuing...");

               await UpdateModixAsync(vehicles);

               await VerifyAllChangesAsync(vehicles);
            }
            else
            {
               logger.Info($"Failed to login, quitting...");
            }

            _driver.Quit();
            _driver.Dispose();
            stopwatch.Stop();

            logger.Info("========================= Completed ModixScrapeJob =====================================");
         }
         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            EmailerService eService = new EmailerService();
            await eService.SendMail("ERROR: WebScraper - ModixUpdatePricesJob.", errorMessage);
            logger.Error($"Problem {e.ToString()}");

            _driver.Quit();
            _driver.Dispose();
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted.
               Project = "WebScraper",
               Customer = "Vindis",
               //Environment = env,
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }

      public async Task<List<ModixPriceUpdate>> GetPriceChangesToProcess(DealerGroupName dealerGroup)
      {
         PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

         GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup);
         var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         // Remove AutoNow or Three10 sites here - they are using Salesmaster
         retailers = retailers.Where(x => !x.Name.Contains("AutoNow") && !x.Name.Contains("Three10")).ToList();
         todayChangesFirstPass.totalChanges = todayChangesFirstPass.totalChanges.Where(x => retailers.Any(y => y.Id == x.RetailerSiteId)).ToList();
         todayChangesFirstPass.overdueChanges = todayChangesFirstPass.overdueChanges.Where(x => retailers.Any(y => y.Id == x.RetailerSiteId)).ToList();
         todayChangesFirstPass.approvedChangesToAction = todayChangesFirstPass.approvedChangesToAction.Where(x => retailers.Any(y => y.Id == x.RetailerSiteId)).ToList();

         //Early return if none
         if (todayChangesFirstPass.totalChanges.Count == 0)
         {
            return new List<ModixPriceUpdate>();
         }

         // There are some changes, so now re-run the vehicle opt-out updater
         var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

         //have to run again in case we just made some optouts
         GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         // Again Remove AutoNow or Three10 sites here - they are using Salesmaster
         todayChangesResponse.totalChanges = todayChangesResponse.totalChanges.Where(x => retailers.Any(y => y.Id == x.RetailerSiteId)).ToList();
         todayChangesResponse.overdueChanges = todayChangesResponse.overdueChanges.Where(x => retailers.Any(y => y.Id == x.RetailerSiteId)).ToList();
         todayChangesResponse.approvedChangesToAction = todayChangesResponse.approvedChangesToAction.Where(x => retailers.Any(y => y.Id == x.RetailerSiteId)).ToList();


         string _connectionString = ConfigService.GetConnectionString(dealerGroup);

         var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);

         List<ModixPriceUpdate> result = todayChangesResponse.totalChanges.ConvertAll(x => new ModixPriceUpdate(x));
         return result;
      }


      private async Task UpdateModixAsync(List<ModixPriceUpdate> vehicles)
      {
         // Call the generic method with Update operation
         await ProcessVehiclesAsync(vehicles, VehicleProcessOperation.Update, "Update Modix");
      }

      private async Task VerifyAllChangesAsync(List<ModixPriceUpdate> vehicles)
      {
         // Call the generic method with Verify operation
         List<ModixPriceUpdate> amendedVehs = vehicles.Where(x => x.PriceChanged).ToList();

         if (amendedVehs.Count > 0)
         {
            logger.Info($"VerifyAllChanges: {amendedVehs.Count} price changes to verify.");
            await ProcessVehiclesAsync(amendedVehs, VehicleProcessOperation.Verify, "Verifying Changes Modix");
         }
         else
         {
            logger.Info($"VerifyAllChanges: No price changes to verify.");
         }

      }

      // Generic method to process vehicles for either updating or verifying
      private async Task ProcessVehiclesAsync(List<ModixPriceUpdate> vehicles, VehicleProcessOperation operation, string operationDescription)
      {
         try
         {
            logger.Info($"Starting {operationDescription}");

            // For verify operation, navigate to home page first
            if (operation == VehicleProcessOperation.Verify)
            {
               _driver.Navigate().GoToUrl(homePageLink);
            }

            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            DateTime start = DateTime.Now;

            var vehiclesBySite = vehicles.ToLookup(x => x.RetailerSiteId);

            // Iterate over each site grouping
            foreach (IGrouping<int, ModixPriceUpdate> siteGroup in vehiclesBySite)
            {
               // If SEAT, we need to split between CUPRA and SEAT regular
               if (siteGroup.Key == seatSiteId)
               {
                  //do the cupra ones separately
                  var cupraGroup = siteGroup.Where(x => x.IsCupra);
                  if (cupraGroup.Any())
                  {
                     logger.Info($"Processing site SEAT CUPRA {cupraGroup.First().RetailerSiteId} with {cupraGroup.Count()} vehicles");
                     await NavigateToSiteAndProcessVehiclesAsync(wait, cupraGroup.ToList(), operation);
                  }

                  _driver.Navigate().GoToUrl(homePageLink);

                  //nonCupra
                  var nonCupra = siteGroup.Where(x => !x.IsCupra);
                  if (nonCupra.Any())
                  {
                     logger.Info($"Processing site SEAT  {nonCupra.First().RetailerSiteId} with {nonCupra.Count()} vehicles");
                     await NavigateToSiteAndProcessVehiclesAsync(wait, nonCupra.ToList(), operation);
                  }
               }
               else
               {
                  logger.Info($"Processing site {siteGroup.Key} with {siteGroup.Count()} vehicles");
                  await NavigateToSiteAndProcessVehiclesAsync(wait, siteGroup.ToList(), operation);
               }

               // Go back to Home Page
               // (should arrive on page with list of brands on left menu)
               _driver.Navigate().GoToUrl(homePageLink);
            }

            logger.Info("Complete iterating over vehicles by site");
         }
         catch (Exception e)
         {
            logger.Info($"Unknown error in {operationDescription}", e);
            throw;
         }
      }

      private async Task<bool> LoginAsync()
      {
         WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
         IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

         try
         {
            _driver.Navigate().GoToUrl(homePageLink + "/login/");

            // Wait for username field to appear and enter email
            var usernameField = wait.Until(ExpectedConditions.ElementIsVisible(By.Name("username")));
            usernameField.SendKeys("<EMAIL>");
            usernameField.SendKeys(Keys.Tab);

            // Wait for password field to appear and enter password
            var passwordField = wait.Until(ExpectedConditions.ElementIsVisible(By.Name("password")));
            passwordField.SendKeys(ConfigService.ModixPassword);

            // Wait for login button to become clickable and click it
            var loginButton = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath("//div[2]/form/div[2]/div[2]/button")));
            loginButton.Click();
         }
         catch (Exception)
         {
            throw; //this gets caught by the main method, which sends an email.
         }


         try
         {
            IWebElement passwordUpdateRequired = wait.Until(ExpectedConditions.ElementExists(By.Name("old_password")));
            EmailerService eService = new EmailerService();
            await eService.SendMail("Modix password update required.", "Please update Modix password.");
            return false;
         }
         catch
         {
            return true;
         }

      }


      // Generic method that handles both updating and verifying prices
      // The vehicles list MUST be grouped by SiteId
      private async Task NavigateToSiteAndProcessVehiclesAsync(WebDriverWait wait, List<ModixPriceUpdate> grouping, VehicleProcessOperation operation)
      {
         int siteId = grouping.First().RetailerSiteId;
         bool isCupra = siteId == seatSiteId && grouping.First().IsCupra;

         string brandName = GetBrand(siteId, isCupra);
         string siteCodeDictionary = GetSiteCode(siteId, isCupra);

         // Click Brand
         WaitAndFind($"//a[contains(text(), '{brandName}')]", true);
         System.Threading.Thread.Sleep(2000);

         // Click Site from List
         WaitAndFind($"//a[contains(text(), '{siteCodeDictionary}')]", true);
         System.Threading.Thread.Sleep(2000);

         // Should arrive at Vehicle management dashboard for site
         // Vehicle management
         WaitAndFind("//span[contains(text(), 'Vehicle management')]", true);
         System.Threading.Thread.Sleep(2000);

         // Vehicle management -> Inventory
         WaitAndFind("//a[@title='Inventory']", true);
         System.Threading.Thread.Sleep(3000);

         // Process each vehicle based on the operation type
         foreach (ModixPriceUpdate veh in grouping)
         {
            bool foundItem = SearchInventory(wait, veh);

            if (foundItem)
            {
               // Perform the appropriate action based on operation type
               if (operation == VehicleProcessOperation.Update)
               {
                  logger.Info($"Found item: {veh.Reg}");
                  AmendPricesAsync(veh);
               }
               else // Verify operation
               {
                  VerifyPrices(wait, veh);
               }

               // Go back to the Inventory to find the next item
               WaitAndFind("//a[@title='Inventory']", true);
            }
            else
            {
               string errorMessage = $"Could not find item: {veh.Reg}";
               logger.Error(errorMessage);

               veh.SavedOk = false;
               veh.SaveError = errorMessage;
            }

            // Save the result to database
            try
            {
               await SaveChangeResult(veh);
               logger.Info($"Saved price change result for {veh.Reg}");
            }
            catch (Exception ex)
            {
               string errorMessage = $"Failed to save change result for {veh.Reg}: {ex.Message}";
               logger.Error(errorMessage);
               EmailerService eService = new EmailerService();
               await eService.SendMail("ERROR: WebScraper - ModixUpdatePricesJob.", errorMessage);
            }

         }

         System.Threading.Thread.Sleep(1000);
      }


      private string GetBrand(int siteId, bool isCupra)
      {
         if (siteId == seatSiteId)
         {
            return isCupra ? "CUPRA UK" : "SEAT UK";
         }
         else if (brandDictionary.ContainsKey(siteId))
         {
            return brandDictionary[siteId];
         }
         else
         {
            logger.Info("GetBrand - Error: Key not found.", null);
            throw new Exception($"GetBrand - Error: Key not found. Key:{siteId}");
         }
      }

      private string GetSiteCode(int siteId, bool isCupra)
      {
         if (siteId == seatSiteId)
         {
            return isCupra ? "70175789" : "1139023";
         }
         else if (siteCodeDictionary.ContainsKey(siteId))
         {
            return siteCodeDictionary[siteId];
         }
         else
         {
            logger.Info("GetSiteCode - Error: Key not found.", null);
            throw new Exception($"GetSiteCode - Error: Key not found. Key:{siteId}");
            
         }
      }

      private Dictionary<int, string> InitiateBrandDictionary()
      {
         // Create a dictionary that holds integer and string pairs
         Dictionary<int, string> result = new Dictionary<int, string>();

         // Populate the dictionary with three entries
         // result.Add(14, "CUPRA UK"); Seperately done for SEAT
         // result.Add(14, "SEAT UK"); // CUPRA is SEAT? Need to clarify
         result.Add(12, "SKODA UK");
         result.Add(13, "SKODA UK");

         // Audi
         result.Add(1, "UK Audi");
         result.Add(2, "UK Audi");
         result.Add(3, "UK Audi");
         result.Add(4, "UK Audi");
         result.Add(5, "UK Audi");

         result.Add(11, "VWCV UK");

         // VW NON-VC
         result.Add(6, "VWPC UK");
         result.Add(7, "VWPC UK");
         result.Add(8, "VWPC UK");
         result.Add(9, "VWPC UK");

         return result;
      }

      private Dictionary<int, string> InitiateSiteCodeDictionary()
      {
         // Create a dictionary that holds integer and string pairs
         Dictionary<int, string> result = new Dictionary<int, string>();

         // Populate the dictionary with three entries
         // result.Add(14, "70175789"); Seperately done for SEAT

         result.Add(12, "3268913");
         result.Add(13, "744203");

         result.Add(1, "21257");
         result.Add(2, "6507");
         result.Add(3, "11255");
         result.Add(4, "1057008");
         result.Add(5, "19654");

         result.Add(11, "3494674");

         result.Add(6, "42145");
         result.Add(7, "42147");
         result.Add(8, "123189");
         result.Add(9, "42148");

         return result;
      }

      private void AmendPricesAsync(ModixPriceUpdate veh)
      {
         logger.Info($"Amending price for: {veh.Reg}");

         WaitAndFind("//div[@class='ml-column ml-row1 ml-col1']/a[2]", true);

         System.Threading.Thread.Sleep(1000);

         string retailPriceFieldName = "Retail price";
         string makeAnOfferFieldName = "Make an offer";

         string regOnPage = GetRegOnUpdatePage();
         string errorMessage = "";
         if (veh.Reg != regOnPage)
         {
            errorMessage = $"AmendPrices Error: Reg on update page does not match reg for: {veh.Reg}";
            logger.Error(errorMessage);

            veh.SavedOk = false;
            veh.SaveError = errorMessage;
            return;
         }

         int retailPrice = veh.Price - 99;
         int offerPrice = retailPrice - 1;

         try
         {
            // Validate that the fields exist
            var retailPriceRow = WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]", false);
            var makeAnOfferRow = WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]", false);

            // Clear each field and then amend them to new price
            WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).Clear();
            WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).SendKeys(retailPrice.ToString() + Keys.Tab);

            WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).Clear();
            WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).SendKeys(offerPrice.ToString() + Keys.Tab);

            // Check we have updated them correctly
            var retailPriceField = WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false);
            string retailPriceValueStr = retailPriceField.GetAttribute("value");
            int retailPriceSet = ConvertPriceToInt(retailPriceValueStr);

            var offerPriceField = WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false);
            string offerPriceValueStr = offerPriceField.GetAttribute("value");
            int offerPriceSet = ConvertPriceToInt(offerPriceValueStr);

            if (retailPrice != retailPriceSet)
            {
               errorMessage = $"AmendPrices Error: Retail Price set ({retailPriceSet}) does not match intended ({retailPrice}) for: {veh.Reg}. Did not save.";
               logger.Error(errorMessage);

               veh.SavedOk = false;
               veh.SaveError = errorMessage;
               return;

            }

            if (offerPrice != offerPriceSet)
            {
               errorMessage = $"AmendPrices Error: Offer Price set ({offerPriceSet}) does not match intended ({offerPrice}) for: {veh.Reg}. Did not save.";
               logger.Error(errorMessage);

               veh.SavedOk = false;
               veh.SaveError = errorMessage;
               return;
            }

         }
         catch (Exception ex)
         {
            errorMessage = $"AmendPrices Error: Unknown error updating price fields for: {veh.Reg}";
            logger.Error(errorMessage, ex);

            veh.SavedOk = false;
            veh.SaveError = errorMessage + ex.Message;
            return;
         }

         System.Threading.Thread.Sleep(500);

         // Hit save
         try
         {
            WaitAndFind("//a[@title='Save']", true);

            logger.Info($"Amended price for: {veh.Reg} to Retail Price: {retailPrice} and Offer Price: {offerPrice}");

            veh.PriceChanged = true;
            veh.SavedOk = true;
         }
         catch (Exception ex)
         {
            logger.Info($"AmendPrices Error: Unknown error saving price for: {veh.Reg}", ex);
            veh.SavedOk = false;
            veh.SaveError = ex.Message;
         }

         
      }

      private void VerifyPrices(WebDriverWait wait, ModixPriceUpdate veh)
      {
         logger.Info($"Verifying price for: {veh.Reg}");

         // Click into the item
         WaitAndFind("//div[@class='ml-column ml-row1 ml-col1']/a[2]", true);

         System.Threading.Thread.Sleep(500);

         string retailPriceFieldName = "Retail price";
         string makeAnOfferFieldName = "Make an offer";

         string regOnPage = GetRegOnUpdatePage();
         string errorMessage = "";

         if (veh.Reg != regOnPage)
         {
            errorMessage = $"VerifyPrices Error: Reg on update page does not match reg for: {veh.Reg}";
            logger.Error(errorMessage);

            veh.SavedOk = false;
            veh.SaveError = errorMessage;
            return;
         }


         int retailPrice = veh.Price - 99;
         int offerPrice = retailPrice - 1;

         try
         {
            // Validate that the field exists
            var retailPriceRow = WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]", false);

            var makeAnOfferRow = WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]", false);

            // Check we have updated them correctly
            var retailPriceField = WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false);

            string retailPriceValueStr = retailPriceField.GetAttribute("value");

            int retailPriceSet = ConvertPriceToInt(retailPriceValueStr);

            var offerPriceField = WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false);

            string offerPriceValueStr = offerPriceField.GetAttribute("value");

            int offerPriceSet = ConvertPriceToInt(offerPriceValueStr);

            if (retailPrice != retailPriceSet)
            {
               errorMessage = $"VerifyPrices Error: Retail Price set ({retailPriceSet}) does not match intended ({retailPrice}) for: {veh.Reg}";
               logger.Error(errorMessage);

               veh.SavedOk = false;
               veh.SaveError = errorMessage;
               return;
            }

            if (offerPrice != offerPriceSet)
            {
               errorMessage = $"VerifyPrices Error: Offer Price set ({offerPriceSet}) does not match intended ({offerPrice}) for: {veh.Reg}";
               logger.Error(errorMessage);

               veh.SavedOk = false;
               veh.SaveError = errorMessage;
               return;
            }

            logger.Info($"VerifyPrices: Verified updated prices match ({retailPriceSet}/{retailPrice}) for: {veh.Reg}");
         }
         catch (Exception ex)
         {
            errorMessage = $"VerifyPrices Error: Unknown error validating price fields for: {veh.Reg}";
            logger.Error(errorMessage, ex);

            veh.SavedOk = false;
            veh.SaveError = errorMessage + ex.Message;
            return;

         }

      }

      private int ConvertPriceToInt(string price)
      {
         // Remove £ and commas, then parse
         string cleaned = price.Replace("£", "").Replace(",", "").Trim();
         return int.Parse(cleaned);
      }


      private string GetRegOnUpdatePage()
      {
         string regXpath = "//input[@name='licenceNumber' and contains(@class, 'dijitInputInner')]";

         var test = WaitAndFind(regXpath, false);

         return test.GetAttribute("value").ToUpper().Trim();
      }


      private bool SearchInventory(WebDriverWait wait, ModixPriceUpdate veh)
      {
         try
         {

            //if (veh.Reg == "FN15YJX")
            //{

            //}

            logger.Info($"Searching item: {veh.Reg}");

            IWebElement inventorySearchBox = wait.Until(ExpectedConditions.ElementExists(By.Id("inventorySearchTxt2")));

            // Clear search box
            inventorySearchBox.Clear();

            System.Threading.Thread.Sleep(500);

            // Search for reg
            WaitAndFind("//input [@Id='inventorySearchTxt2']", false).SendKeys(veh.Reg + Keys.Enter);

            // Takes a second or so to return
            System.Threading.Thread.Sleep(2000);

            // If this isn't here, we don't have any entries
            try
            {
               IWebElement result = WaitAndFind("(//*[contains(concat(' ', normalize-space(@class), ' '), ' ml-group ml-level0 ')])[1]", false);
            }
            catch
            {
               logger.Info($"No entries found for: {veh.Reg}");
               return false;
            }

            // Path to get to the data panel of first entry
            string xpath = "//ul[@id='inventoryList']/li[contains(@class, 'ml-row')][1]" +
               "//div[contains(@class, 'ml-innerRow') and contains(@class, 'ml-row2')]" +
               "//div[contains(@class, 'ml-column') and contains(@class, 'ml-col2')]" +
               "//div[contains(@class, 'content')]/div[contains(@class, 'data')][3]";

            IWebElement dataElement = WaitAndFind(xpath, false);

            string dataStringUnclean = dataElement.Text.Trim().ToUpper();

            string reg = ExtractReg(dataStringUnclean);

            if (reg == veh.Reg)
            {
               logger.Info($"Found item in Inventory: {veh.Reg}");
               return true;
            }

            logger.Info($"No matching entries found: {veh.Reg}");
            return false;
         }
         catch (Exception ex)
         {
            logger.Info($"Error finding: {veh.Reg}");
            return false;
         }
      }

      private string ExtractReg(string text)
      {
         string[] lines = text.Split(new[] { "\r\n" }, StringSplitOptions.None);

         return lines.FirstOrDefault(line => !string.IsNullOrWhiteSpace(line)) ?? string.Empty;
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         return ScraperMethodsService.WaitAndFind(_driver, "ModixUpdatePrices", findXPath, andClick);
      }

      private static async Task SaveChangeResult(ModixPriceUpdate change)
      {
         using (var db = new CPHIDbContext(ConfigService.GetConnectionString(DealerGroupName.Vindis)))
         {
            if (change.IsAutoPriceChange)
            {
               var dbItem = db.PriceChangeAutoItems.FirstOrDefault(x => x.Id == change.PriceChangeId);
               if (dbItem == null)
               {
                  throw new Exception("Db item not found, are you running in debug with dummy items?");
               }
               if (change.SavedOk)
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.DateConfirmed = DateTime.UtcNow;
                  dbItem.SaveResult = "OK";

               }
               else
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.SaveResult = change.SaveError.Length > 500 ? change.SaveError.Substring(0, 500) : change.SaveError;
               }
            }
            else
            {
               var dbItem = db.PriceChangeManualItems.FirstOrDefault(x => x.Id == change.PriceChangeId);
               if (dbItem == null)
               {
                  throw new Exception("Db item not found, are you running in debug with dummy items?");
               }
               if (change.SavedOk)
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.DateConfirmed = DateTime.UtcNow;
                  dbItem.SaveResult = "OK";
               }
               else
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.SaveResult = change.SaveError.Length > 500 ? change.SaveError.Substring(0, 500) : change.SaveError;
               }
            }

            await db.SaveChangesAsync();
         }
      }

   }
}
