﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
    public class BookingJobsJobVindis : IJob
    {


        private static readonly ILog Logger = LogManager.GetLogger(typeof(BookingJobsJobVindis));
        private const string fileSearch = "*-export2.xls";
        /*
| Column   | Example                          | Sensitive |
|----------|----------------------------------|-----------|
| Site     | Skoda Service Centre Waterbeach  | No        |
| Date     | 2023-04-20                       | No        |
| Bookings | 20                               | No        |

*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            string[] filePaths = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot, fileSearch ));

            //check for presence of file, if so, return as already running
            if (LocksService.BookingJobs) { CentralLoggingService.ReportLock("BookingJobsJobVindis"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.BookingJobs;
                if (age.Minutes > 120)
                {
                    PulsesService.BookingJobs = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *-export2.xls");
                }
                return;
            }


            //define Lists
            List<Site> dbSites;
            List<DailySiteBookingStat> dbDailySiteBookingStats;

            using (var db = new CPHIDbContext())

            {
                int errorCount = 0;
                LogMessage logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 3;

                try
                {

                    dbSites = db.Sites.ToList();
                    dbDailySiteBookingStats = db.DailySiteBookingStats.ToList();

                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found

                    //go through first file
                    string filePath = filePaths[0];

                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;

                    if (File.Exists(filePath.Replace(".xls", "-p.xls")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".xls", "-p.xls")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".xls", "-p.xls");
                    LocksService.BookingJobs = true;

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<DailySiteBookingStat> incomingBookingJobs = new List<DailySiteBookingStat>(1000);  //preset the list size (slightly quicker than growing it each time)

                    HeadersAndRows rowsFromHelper = GetDataFromFilesService.GetExcelFileContents(newFilepath, 0, 1, null);

                    List<string> headers = rowsFromHelper.headers;

                    List<List<string>> rows = rowsFromHelper.rows;

                    int siteIndex = headers.IndexOf("Site");
                    int dateIndex = headers.IndexOf("Date");
                    int bookingsIndex = headers.IndexOf("Bookings");

                    foreach (var row in rows)
                    {
                        incomingCount++;

                        try
                        {

                            if (row.Count != headers.Count)
                            {
                                //something weird happened, not got enough cells for the number of headerCols, skip this record
                                logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {row.Count} cells and needed {headers.Count} file is {filePath}";
                                errorCount++;
                                continue;
                            }

                            //lookup objects required

                            string siteName = row[siteIndex];
                            DateTime date = DateTime.ParseExact(row[dateIndex], "yyyy-MM-dd", null);
                            decimal bookings = decimal.Parse(row[bookingsIndex]);

                            int siteId = 0;

                            //branch & department
                            switch (siteName)
                            {
                                case "Bedford Audi": { siteId = 1; break; }
                                case "Bedford Volkswagen": { siteId = 6; break; }
                                case "Bury St Edmunds Skoda": { siteId = 12; break; }
                                case "Cambridge Audi": { siteId = 2; break; }
                                case "Fakenham Volkswagen": { siteId = 8; break; }
                                case "Huntingdon Audi": { siteId = 3; break; }
                                case "Huntingdon CV": { siteId = 10; break; }
                                case "Huntingdon Commercial vehicles": { siteId = 10; break; }
                                case "Huntingdon Volkswagen": { siteId = 9; break; }
                                case "Milton Keynes Seat": { siteId = 14; break; }
                                case "Northampton Audi": { siteId = 4; break; }
                                case "Northampton CV": { siteId = 11; break; }
                                case "Northampton Commercial Vehicles": { siteId = 11; break; }
                                case "Northampton Commerical Vehicles": { siteId = 11; break; } // It has been sent with this mispelling previously so kept in
                                case "Peterborough Audi": { siteId = 5; break; }
                                case "Skoda Service Centre Cambridge": { siteId = 13; break; }
                                case "Skoda Service Centre Waterbeach ": { siteId = 13; break; }
                                case "Volkswagen Service Centre Waterbeach": { siteId = 7; break; }
                                case "Volkswagen Service Centre Cambridge": { siteId = 7; break; }
                                case "Bentley Cambridge": { siteId = 16; break; }
                                case "Three10 Automotive": { siteId = 16; break; }
                                case "Three 10 Automotive": { siteId = 16; break; }
                                case "Three 10 Automotive Bentley Cambridge": { siteId = 16; break; }
                                case "Ducati Cambridge": { siteId = 15; break; }
                                default: { continue; }
                            }

                            if (siteId == 0) { continue; } //if no matching siteId found then skip this row

                            DailySiteBookingStat b = new DailySiteBookingStat(); //initialise new one

                            b.Day = date;
                            b.Availability = 0;
                            b.JobsBooked = bookings;
                            b.Site_Id = siteId;
                            b.HoursBooked = 0;

                            incomingBookingJobs.Add(b);

                            Console.WriteLine(incomingCount);

                        }

                        catch (Exception err)
                        {
                            if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on updating item, Booking Jobs: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                            errorCount++;
                            continue;
                        }
                    }

                    List<DailySiteBookingStat> newItems = incomingBookingJobs;

                    newCount = newItems.Count;

                    //find earliest incoming
                    DateTime earliestIncomingBooking = newItems.OrderBy(x => x.Day).First().Day;

                    foreach (DailySiteBookingStat stat in db.DailySiteBookingStats.Where(x => x.Day >= earliestIncomingBooking))
                    {
                        try { stat.JobsBooked = newItems.Find(y => y.Site_Id == stat.Site_Id && y.Day == stat.Day).JobsBooked; } catch { }
                    }

                    try
                    {
                        db.SaveChanges();
                    }


                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                        errorCount++;
                    }

                    logMessage.FinishDate = DateTime.UtcNow;
                    logMessage.ProcessedCount = incomingBookingJobs.Count;
                    logMessage.AddedCount = newCount;
                    logMessage.RemovedCount = removedCount;
                    logMessage.IsCompleted = true;
                    logMessage.ErrorCount = errorCount;


                    Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingBookingJobs.Count} item(s) interpreted, added {newCount} new");
                    try
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xls", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xls"));
                        if (errorCount > 0)
                        {
                            //we have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            await CentralLoggingService.ReportError("BookingsJobs", logMessage, true);
                            
                        }
                        else
                        {
                            //no errors so just save the log
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            
                        }
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;
                        
                            await CentralLoggingService.ReportError("BookingsJobs", logMessage, true);
                    }

                    //trigger cache rebuild
                    await UpdateWebAppService.Trigger("DailySiteBookingStats");
                    stopwatch.Stop();
                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();
                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                    
                    await CentralLoggingService.ReportError("BookingsJobs", logMessage);

                }
                finally
                {
                    db.ChangeTracker.Clear();
                    LocksService.BookingJobs = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "Vindis",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };

                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }



            }
        }



    }


}
