export class CompetitorVehicle {

   constructor(existingVehicle: CompetitorVehicle, newAdvertisedPrice: number, newPricePosition: number) {
      this.WebsiteSearchIdentifier = existingVehicle.WebsiteSearchIdentifier;
      this.ImageURL = existingVehicle.ImageURL;
      this.CompetitorName = existingVehicle.CompetitorName;
      this.Segment = existingVehicle.Segment;
      this.VehicleReg = existingVehicle.VehicleReg;
      this.Year = existingVehicle.Year;
      this.SeptemberPlateChange = existingVehicle.SeptemberPlateChange;
      this.Mileage = existingVehicle.Mileage;
      this.AdvertisedPrice = newAdvertisedPrice;
      this.PricePosition = newPricePosition;
      this.Distance = existingVehicle.Distance;
      this.IsOurVehicle = existingVehicle.IsOurVehicle;
      this.IsTradeAdjusted = false;
      this.VehicleType = existingVehicle.VehicleType;
      this.TotalAdvertCount = existingVehicle.TotalAdvertCount;
      this.IsVehicleWeSold = existingVehicle.IsVehicleWeSold;
   }

   WebsiteSearchIdentifier: string;
   ImageURL: string;
   CompetitorName: string;
   CompetitorId: string;
   Segment: string;
   VehicleReg:string;
   Year: number | null;
   SeptemberPlateChange: boolean | null;
   Mileage: number;
   AdvertisedPrice: number ;
   TradePrice: number ;
   TradeAdjustmentPercentage: number;
   
   TradeAdjustmentAmount: number;
   PricePosition: number ;
   Distance: number;
   IsOurVehicle: boolean;
   IsTradeAdjusted: boolean;
   TradePricing: boolean;
   VehicleType: string;
   TotalAdvertCount: number;
   IsVehicleWeSold:boolean;
   isFetchingTotalAdverts: boolean = false;
   Derivative: string;
   FuelType:string;
   BodyType:string;
   TransmissionType:string;
   Doors:string;
   Trim:string;
   Town:string;
   Region:string;
   Postcode:string;
}


export class CompetitorVehicleDisplay{
 
   WebsiteSearchIdentifier: string;
   ImageURL: string;
   CompetitorName: string;
   CompetitorId: string;
   Segment: string;
   Year: number | null;
   SeptemberPlateChange: boolean | null;
   Mileage: number;
   AdvertisedPrice: string ;
   PricePosition:  string;
   Distance: number;
   IsOurVehicle: boolean;
   IsTradeAdjusted: boolean;
   VehicleType: string;
   TotalAdvertCount: number;
   IsVehicleWeSold:boolean;
   isFetchingTotalAdverts: boolean = false;
}
