﻿using CPHI.Spark.Model;
using Dapper;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Repository;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using StockPulse.WebApi.Model.ViewModels;
using Microsoft.AspNetCore.Http.HttpResults;
using System.Data;

namespace CPHI.Spark.DataAccess
{
   public interface IUserDataAccess
   {
      Task AddSalesRoles(List<SalesRole> salesRoles, Model.DealerGroupName dealerGroup);
      Task<int> CreatePerson(UserAndLogin user, Model.DealerGroupName dealerGroup);
      Task DeleteSalesroles(DateTime startDate, Person person, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<UserAndLogin>> GetAllUsersAndLogins(Model.DealerGroupName dealerGroup);
      Task<Helpers.Languages?> GetLanguage(int linkedPersonId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SalesRoleVM>> GetSalesRolesByYear(int year, Model.DealerGroupName dealerGroup);
      Task<Person> GetUser(string linkedPersonId, Model.DealerGroupName dealerGroup);
      Task<string> GetUserEmailAddress(int userId, Model.DealerGroupName dealerGroup);
      Task<int> GetUserRetailerSiteId(int userId, Model.DealerGroupName dealerGroup);
      Task<int> GetUserRetailerSiteRetailerId(int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<int>> GetUserRetailSiteIds(int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<int>> GetUserSiteIds(int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<string>> GetUserSitesName(int userId, Model.DealerGroupName dealerGroup);
      //Task<string> GetUsersName(int linkedPersonId, DealerGroupName dealerGroup);
      Task UpdateLanguage(int linkedPersonId, Helpers.Languages language, Model.DealerGroupName dealerGroup);
      Task UpdateSalesRoles(List<SalesRole> salesRoles, Model.DealerGroupName dealerGroup);
      Task UpdatePerson(int userId, UserAndLogin user, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<UserSimple>> GetAllUsersSimple(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<ClaimVM>> GetUsersClaims(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<UserParamSet>> GetUserSiteRole();
      Task<IEnumerable<ClaimTypeAndValues>> GetAllClaimTypes(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<RetailerSite>> GetUserRetailSites(List<int> userSiteIds, Model.DealerGroupName dealerGroup);
      Task<List<UsageItem>> GetUsageItemsThisWeek(int dealerGroupId);
      Task<List<UsageSummaryItem>> GetUsageSummaryItems(int dealerGroupId);
      Task<List<Person>> GetUsers(List<int> userIds, DealerGroupName dealerGroup);
      Task<ClaimVM> GetUserClaim(DealerGroupName dealerGroup, int userId, string claimType);
      Task<int> GetUserCurrentSiteId(int userId, DealerGroupName dealerGroup);
   }

   public class UserDataAccess : IUserDataAccess
   {
      private readonly string _connectionString;
      public UserDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }

      private static bool getBoolFromNullable(bool? itemIn)
      {
         if (itemIn == null) { return false; }
         return itemIn.Value;
      }


      //UnifiedDB - no update required
      public async Task<int> CreatePerson(UserAndLogin user, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("Name", user.Name);
            paramList.Add("CurrentSiteId", user.SiteId);
            paramList.Add("Sites", user.Sites);
            paramList.Add("Role", user.RoleName);
            paramList.Add("Email", user.Email);
            paramList.Add("IsSalesExec", getBoolFromNullable(user.IsSalesExec));
            paramList.Add("AccessAllSites", getBoolFromNullable(user.AccessAllSites));
            paramList.Add("JobTitle", user.JobTitle);
            paramList.Add("IsTMgr", getBoolFromNullable(user.IsTMgr));
            paramList.Add("DealerGroupId", (int)dealerGroup);
            paramList.Add("RetailerSiteId", user.RetailerSiteId);

            return await dapper.GetAsync<int>("dbo.CREATE_Person", paramList, System.Data.CommandType.StoredProcedure);
         }
      }



      //public async Task<string> GetUsersName(int linkedPersonId, DealerGroupName dealerGroup)
      //{
      //    using (var dapper = new DADapperr(_connectionString))
      //    {
      //        return await dapper.GetAsync<string>($"SELECT Name FROM People WHERE Id = {linkedPersonId}", null, System.Data.CommandType.Text);
      //    }
      //}


      //UnifiedDB - no update required
      public async Task<IEnumerable<UserAndLogin>> GetAllUsersAndLogins(Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("dealerGroupId", (int)dealerGroup);

            var res = await dapper.GetAllAsync<UserAndLogin>("dbo.GET_AllUsersAndLogins", paramList, System.Data.CommandType.StoredProcedure);
            return res;
         }
      }

      //unifedDB - updated
      public async Task UpdatePerson(int userId, UserAndLogin userAndLogin, Model.DealerGroupName dealerGroup)
      {

         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("Name", userAndLogin.Name);
            paramList.Add("CurrentSiteId", userAndLogin.SiteId);
            paramList.Add("Sites", userAndLogin.Sites);
            paramList.Add("Role", userAndLogin.RoleName);
            paramList.Add("Email", userAndLogin.Email);

            paramList.Add("JobTitle", userAndLogin.JobTitle);
            paramList.Add("RetailerSiteId", userAndLogin.RetailerSiteId);
            // Booleans
            paramList.Add("IsSalesExec", userAndLogin.IsSalesExec.HasValue ? userAndLogin.IsSalesExec : false);

            paramList.Add("AccessAllSites", userAndLogin.AccessAllSites.HasValue ? userAndLogin.AccessAllSites : false);
            paramList.Add("IsTMgr", userAndLogin.IsTMgr.HasValue ? userAndLogin.IsTMgr : false);
            paramList.Add("DealerGroupId", (int)dealerGroup);

            await dapper.ExecuteAsync("dbo.UPDATE_Person", paramList, System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<UsageItem>> GetUsageItems(int dealerGroupId, GetUsageItemsParams parms)
      {

         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("DealerGroupId", dealerGroupId);
            paramList.Add("PageNames", parms.PageNames != null ? string.Join(",", parms.PageNames) : null);

            return await dapper.GetAllAsync<UsageItem>("dbo.GET_UsageItems", paramList, CommandType.StoredProcedure);
         }

      }

      //UnifiedDB - no update required
      public async Task<List<UsageItem>> GetUsageItemsThisWeek(int dealerGroupId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("DealerGroupId", dealerGroupId);

            var allItems = await dapper.GetAllAsync<UsageItem>("dbo.GET_UsageItems", paramList, CommandType.StoredProcedure);
            DateTime today = DateTime.Now.Date;
            DateTime startOfWeek = GetStartOfWeek(today);
            return allItems.Where(x => x.UsageDate >= startOfWeek).ToList();
         }
      }

      //UnifiedDB - no update required
      public async Task<List<UsageSummaryItem>> GetUsageSummaryItems(int dealerGroupId)
      {

         using (var dapper = new DADapperr(_connectionString))
         {
            DateTime today = DateTime.Now;
            DateTime startOfWeek = GetStartOfWeek(today);
            var paramList = new DynamicParameters();
            paramList.Add("DealerGroupId", dealerGroupId);
            paramList.Add("StartOfWeek", startOfWeek);


            var allItems = await dapper.GetAllAsync<UsageSummaryItem>("dbo.GET_UsageSummaryItems", paramList, CommandType.StoredProcedure);
            return allItems.ToList();
         }


      }




      private DateTime GetStartOfWeek(DateTime date)
      {
         int offset = date.DayOfWeek == DayOfWeek.Sunday ? -6 : DayOfWeek.Monday - date.DayOfWeek;
         return date.AddDays(offset).Date;
      }




      //UnifiedDB - updated
      public async Task<IEnumerable<string>> GetUserSitesName(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT Description FROM Sites WHERE ID IN (SELECT * FROM STRING_SPLIT((SELECT Sites FROM People WHERE ID = {userId}),',')) AND DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAllAsync<string>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<int>> GetUserSiteIds(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT CAST(Value as int) as Id FROM STRING_SPLIT((SELECT Sites FROM People WHERE ID = {userId} AND DealerGroup_Id = {(int)dealerGroup}),',') order by Id";
            return await dapper.GetAllAsync<int>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<int> GetUserCurrentSiteId(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT CurrentSite_Id FROM People WHERE ID = {userId} AND DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAsync<int>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<RetailerSite>> GetUserRetailSites(List<int> userSiteIds, Model.DealerGroupName dealerGroup)
      {
         string userSites = string.Join(",", userSiteIds);
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $@"SELECT
                        * 
                        FROM autoprice.RetailerSites rs
                        WHERE rs.Site_Id IN ({userSites}) 
                        AND rs.DealerGroup_Id = {(int)dealerGroup}
                        ";
            return await dapper.GetAllAsync<RetailerSite>(query, null, System.Data.CommandType.Text);
         }
      }


      //UnifiedDB - updated
      public async Task<string> GetUserEmailAddress(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT TOP 1 a.Email from AspNetUsers a INNER JOIN People p on p.Id = a.LinkedPersonId where a.LinkedPersonId = {userId} and p.DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<UserDetails> GetUserDetails(int linkedPersonId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            DynamicParameters spParams = new DynamicParameters(new { linkedPersonId = linkedPersonId, dealerGroupId = (int)dealerGroup });
            try
            {

               return await dapper.GetAsync<UserDetails>("dbo.GET_UserDetails", spParams, System.Data.CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
               throw;
            }
         }
      }

      //unifiedDB - no update required
      public async Task<IEnumerable<UserParamSet>> GetUserSiteRole()
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAllAsync<UserParamSet>("dbo.GET_UserSiteRoleToCache", null, System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<List<Person>> GetUsers(List<int> userIds, Model.DealerGroupName dealerGroup)
      {
         string userIdsString = string.Join(",", userIds);
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT * from People where Id IN ({userIdsString}) and DealerGroup_Id = {(int)dealerGroup}";
            return (await dapper.GetAllAsync<Person>(query, null, System.Data.CommandType.Text)).ToList();
         }
      }

      //UnifiedDB - updated
      public async Task<Person> GetUser(string linkedPersonId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT * from People where Id = {linkedPersonId} and DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAsync<Person>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<Helpers.Languages?> GetLanguage(int linkedPersonId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT Language from People where Id = {linkedPersonId} and DealerGroup_Id = {(int)dealerGroup}";
            var val = await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);

            if (val == null)
            {
               val = "0"; //english
            }
            Helpers.Languages lang;
            Enum.TryParse(val, out lang);
            return lang;
         }

      }

      //UnifiedDB - updated
      public async Task UpdateLanguage(int linkedPersonId, Helpers.Languages language, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"UPDATE People SET Language = '{(int)language}' where Id = {linkedPersonId} and DealerGroup_Id = {(int)dealerGroup}";
            await dapper.ExecuteAsync(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SalesRoleVM>> GetSalesRolesByYear(int year, Model.DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var result = await db.SalesRoles
            .Join(
                db.Sites.Where(site => site.DealerGroup_Id == (int)dealerGroup),
                salesRole => salesRole.SiteId,  // Property on SalesRole
                site => site.Id,                // Property on Site
                (salesRole, site) => new SalesRoleVM // Result selector
                {
                   // Select properties you need in the resulting object
                   Id = salesRole.Id,
                   Year = salesRole.Year,
                   Month = salesRole.Month,
                   Role = salesRole.Role,
                   PersonId = salesRole.PersonId,
                   SiteId = salesRole.SiteId,
                   SiteDescription = site.Description
                }
            )
            .Where(x => x.Year == year)
            .ToListAsync();

            return result;
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<ClaimVM>> GetUsersClaims(Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT UserId, ClaimType, ClaimValue FROM [dbo].[AspNetUserClaims] ac INNER JOIN [dbo].[AspNetUsers] au on au.Id = ac.UserId INNER JOIN [dbo].[People] p ON p.Id = au.LinkedPersonId WHERE p.DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAllAsync<ClaimVM>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<ClaimVM> GetUserClaim(Model.DealerGroupName dealerGroup, int userId, string claimType)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT UserId, ClaimType, ClaimValue FROM [dbo].[AspNetUserClaims] ac INNER JOIN [dbo].[AspNetUsers] au on au.Id = ac.UserId INNER JOIN [dbo].[People] p ON p.Id = au.LinkedPersonId WHERE p.DealerGroup_Id = {(int)dealerGroup} AND p.Id = {userId} AND ac.ClaimType = '{claimType}'";
            return await dapper.GetAsync<ClaimVM>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - TODO: DelerGroup check
      public async Task UpdateSalesRoles(List<SalesRole> salesRoles, Model.DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            db.SalesRoles.UpdateRange(salesRoles);
            await db.SaveChangesAsync();
         }
      }

      //UnifiedDB - TODO: DelerGroup check
      public async Task AddSalesRoles(List<SalesRole> salesRoles, Model.DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.SalesRoles.AddRangeAsync(salesRoles);
            await db.SaveChangesAsync();
         }
      }

      //UnifiedDB - updated
      public async Task DeleteSalesroles(DateTime startDate, Person person, Model.DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            IEnumerable<SalesRole> srsToDelete = db.SalesRoles.Where(s => s.PersonId == person.Id && (s.Year == startDate.Year && s.Month > startDate.Month || s.Year > startDate.Year) && s.Person.DealerGroup_Id == (int)dealerGroup);
            db.SalesRoles.RemoveRange(srsToDelete);
            await db.SaveChangesAsync();
         }
      }

      //UnifiedDB - updated
      public async Task<int> GetUserRetailerSiteRetailerId(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $@"SELECT
                TOP 1 RetailerId
                FROM autoprice.RetailerSites
                WHERE Site_Id = (SELECT Site_Id FROM People where Id = {userId}
                AND DealerGroup_Id = {(int)dealerGroup})
                ;";
            //used to need need to avoid getting Alpine sites with  AND SUBSTRING(Name,1,1) <> 'A'
            return await dapper.GetAsync<int>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<int>> GetUserRetailSiteIds(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $@"SELECT
                rs.Id
                FROM autoprice.RetailerSites rs
                WHERE rs.Site_Id IN 
                (
	                SELECT TOP 1000 CAST(Value as int) as Id FROM STRING_SPLIT((SELECT Sites FROM People WHERE ID = {userId}),',') order by Id
                )
               AND rs.DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAllAsync<int>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<int> GetUserRetailerSiteId(int userId, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $@"SELECT
                TOP 1 Id
                FROM autoprice.RetailerSites
                WHERE Site_Id = (SELECT Site_Id FROM People where Id = {userId});
                AND rs.DealerGroup_Id = {(int)dealerGroup}";
            return await dapper.GetAsync<int>(query, null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<UserSimple>> GetAllUsersSimple(Model.DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var result = await db.People
               .Where(x => x.DealerGroup_Id == (int)dealerGroup)
                .Select(person => new UserSimple { Id = person.Id, Name = person.Name })
                .ToListAsync();
            return result;
         }
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<ClaimTypeAndValues>> GetAllClaimTypes(Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var query = $"SELECT ClaimType,ClaimValues FROM DealerGroupClaimTypes WHERE DealerGroupId = {(int)dealerGroup}";
            return await dapper.GetAllAsync<ClaimTypeAndValues>(query, null, System.Data.CommandType.Text);

         }

      }


      //UnifiedDB - TODO dealerGroup Check
      public async Task<UserPreference> SaveUserPreference(UserPreference userPreference)
      {

         //UserPreference pref = new UserPreference(userPreference);


         using (var db = new CPHIDbContext(_connectionString))
         {

            var existing = db.UserPreferences.FirstOrDefault(x => x.PreferenceName == userPreference.PreferenceName && x.Person_Id == userPreference.Person_Id);
            if (existing == null)
            {
               //is a new one
               db.UserPreferences.Add(userPreference);
               await db.SaveChangesAsync();
               return userPreference;
            }
            else
            {
               //is existing one
               existing.Preference = userPreference.Preference;
               await db.SaveChangesAsync();
               return existing;

            }
         }
      }


      //public async Task SavePreferences(int userId, List<UserPreferenceVM> preferences)
      //{
      //    using (var db = new CPHIDbContext(_connectionString))
      //    {
      //        foreach (var preferenceVM in preferences)
      //        {
      //            // Find existing preference
      //            var existingPreference = await db.UserPreferences
      //                .FirstOrDefaultAsync(up => up.Person_Id == userId && up.PreferenceName == preferenceVM.PreferenceName);

      //            // Convert parsed properties back to string for storage
      //            string preferenceString = ConvertPreferenceValue(preferenceVM);

      //            if (existingPreference != null)
      //            {
      //                // Update existing preference
      //                existingPreference.Preference = preferenceString;
      //                existingPreference.PreferenceType = preferenceVM.PreferenceType;
      //            }
      //            else
      //            {
      //                // Create new preference
      //                var newUserPreference = new UserPreference
      //                {
      //                    Person_Id = userId,
      //                    Preference = preferenceString,
      //                    PreferenceName = preferenceVM.PreferenceName,
      //                    PreferenceType = preferenceVM.PreferenceType
      //                };
      //                db.UserPreferences.Add(newUserPreference);
      //            }
      //        }

      //        // Save changes to the database
      //        await db.SaveChangesAsync();
      //    }
      //}
   }
}
