<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
        {{service.chosenDerivative.name}}

    </h4>

    <button type="button" class="close" aria-label="Close" (click)="closeModal()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>




<div class="modal-body" [ngClass]="constantsService.environment.customer">
    <!-- Once loaded -->

     <!-- Vehicle Details Card -->
     <div id="vehicleDetails" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="vehicleDetails">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Vehicle Details</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <vehicleDetailsInModal [vehicleInformation]="service.modalData.VehicleInformation"></vehicleDetailsInModal>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>

    <!-- Summary -->
    <div id="summary" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="summary">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Cost Summary</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>

                        <div id="summaryTablesHolder">
                            <div class="summaryChartHolder">
                            <table id="currentList">

                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>Basic</th>
                                        <th>VAT</th>
                                        <th>Total</th>
                                    </tr>

                                </thead>

                                <tbody>
                                    <tr>
                                        <td>Price</td>
                                        <td>{{currentCost.basicPrice|cph:'currency':0}}</td>
                                        <td> {{currentCost.vatPrice|cph:'currency':0}}</td>
                                        <td> {{currentCost.basicPrice + currentCost.vatPrice|cph:'currency':0}}</td>
                                    </tr>
                                    <tr>
                                        <td>Delivery</td>
                                        <td>{{currentCost.deliveryPrice|cph:'currency':0}}</td>
                                        <td> {{currentCost.vatDeliveryPrice|cph:'currency':0}}</td>
                                        <td> {{currentCost.deliveryPrice +
                                            currentCost.vatDeliveryPrice|cph:'currency':0}}</td>
                                    </tr>
                                    <tr class="totalRow">
                                        <td>Total</td>
                                        <td>{{currentCost.basicPrice + currentCost.deliveryPrice|cph:'currency':0}}</td>
                                        <td> {{currentCost.vatPrice + currentCost.vatDeliveryPrice|cph:'currency':0}}
                                        </td>
                                        <td> {{currentCost.basicPrice + currentCost.vatPrice + currentCost.deliveryPrice
                                            + currentCost.vatDeliveryPrice|cph:'currency':0}}</td>
                                    </tr>
                                </tbody>

                            </table>
                            </div>

                            <div class="summaryChartHolder">
                                <table id="valueTable">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>1yr</th>
                                            <th>2yr</th>
                                            <th>3yr</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>6k per annum</td>
                                            <td>{{service.modalData.future6k[11]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future6k[23]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future6k[35]?.RetailValue|cph:'currency':0}}</td>
                                        </tr>
                                        <tr>
                                            <td>10k per annum</td>
                                            <td>{{service.modalData.future10k[11]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future10k[23]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future10k[35]?.RetailValue|cph:'currency':0}}</td>
                                        </tr>
                                        <tr>
                                            <td>20k per annum</td>
                                            <td>{{service.modalData.future20k[11]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future20k[23]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future20k[35]?.RetailValue|cph:'currency':0}}</td>
                                        </tr>
                                        <tr>
                                            <td>30k per annum</td>
                                            <td>{{service.modalData.future30k[11]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future30k[23]?.RetailValue|cph:'currency':0}}</td>
                                            <td>{{service.modalData.future30k[35]?.RetailValue|cph:'currency':0}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="summaryChartHolder">
                            <table id="percentTable">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>1yr</th>
                                        <th>2yr</th>
                                        <th>3yr</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>6k per annum</td>
                                        <td>{{service.modalData.future6k[11]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future6k[23]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future6k[35]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>10k per annum</td>
                                        <td>{{service.modalData.future10k[11]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future10k[23]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future10k[35]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>20k per annum</td>
                                        <td>{{service.modalData.future20k[11]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future20k[23]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future20k[35]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>30k per annum</td>
                                        <td>{{service.modalData.future30k[11]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future30k[23]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                        <td>{{service.modalData.future30k[35]?.PercentageChangeVsTodaysRetailValue|cph:'percent':0}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>

                        </div>


                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>



        <!-- Competitor Vehicles Card -->
    <div id="valuationAndMetrics" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="sellingProspects">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Competitor Vehicles</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <competitorAnalysis [inModal]="true" *ngIf="service.modalData.CompetitorCheckResult"></competitorAnalysis>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>

    
    <!-- Future Valuation Chart -->
    <div id="valuationAndMetrics" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="futureValuesChart">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Future Values Charted</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <newFutureValuationChart>
                        </newFutureValuationChart>

                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>
    <!-- Future Valuation Table -->
    <div id="valuationAndMetrics" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="futureValuesTable">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Future Values Table</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <futureValuationTable>
                        </futureValuationTable>

                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>
    <!-- Features and options -->
    <div id="valuationAndMetrics" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="options">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Features and options</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <div id="optionsTableHolder">
                            <vehicleOptionsTable [data]="getDataForOptionsTable">
                            </vehicleOptionsTable>
                        </div>

                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>


    <!-- Historic prices -->
    <div id="valuationAndMetrics" class="autotraderCard">
        <div class="cardInner">
            <ngb-accordion #acc="ngbAccordion" [activeIds]="activePanels">
                <ngb-panel id="historicPrices">
                    <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                            <div class="factorName layerName">
                                <h3>Historic New Prices</h3>
                            </div>

                            <div class="layerButton"></div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <historicPricesChart>
                        </historicPricesChart>

                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>



</div>


<div class="modal-footer">
    <!-- <button type="button" class="btn btn-success" (click)="saveValuation()">Save
        Valuation</button> -->
    <button type="button" class="btn btn-primary" (click)="closeModal()">Close</button>
</div>