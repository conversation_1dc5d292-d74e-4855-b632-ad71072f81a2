﻿using System;
using System.Linq;
using System.Data;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Repository;
using CPHI.Spark.Model;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels;
using Dapper;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IVehicleAdvertSnapshotsDataAccess
   {
      Task<List<VehicleAdvertSnapshot>> GetDailyVehicleSnapshots(DateTime runDay, DealerGroupName dealerGroup);
      Task<List<VehicleAdvertSnapshotDateAndPrice>> UpdateDailyPriceMoves(DealerGroupName dealerGroup);
      Task<VehicleAdvertSnapshot> GetVehicleSnapshot(string websiteStockIdentifier);
      Task SaveCompetitorInfo(List<VehicleAdvertCompetitorSummary> items);
      Task SaveNewSnapshots(List<VehicleAdvertSnapshot> newSnapshots);
      Task SaveUpdatedDaysToSell(List<DaysToSellRequest> adverts);
      Task SaveUpdatedStrategyForSnapshots(List<VehicleAdvertWithRating> snapshots, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDict);
      Task SetVehicleSnapshotToNotLatest(int snapshotId);
      Task<VehicleAdvertSnapshot> GetSnapshot(int snapshotId);
      Task<List<VehicleAdvertSnapshotWithStockNumber>> GetLatestSnapshots(DateTime monthStart, DealerGroupName dealerGroup);
      Task CreateNewRatingsForUpdatedPrices(IEnumerable<PricingChangeMinimal> okChanges);
   }

   public class VehicleAdvertSnapshotsDataAccess : IVehicleAdvertSnapshotsDataAccess
   {

      private readonly string _connectionString;
      public VehicleAdvertSnapshotsDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }

      public async Task<VehicleAdvertSnapshot> GetSnapshot(int snapshotId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdvertSnapshots.FirstOrDefaultAsync(x => x.Id == snapshotId);
         }
      }

      public async Task<List<VehicleAdvertSnapshot>> GetDailyVehicleSnapshots(DateTime runDay, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var vehicleAdvertSnapshots = await db.VehicleAdvertSnapshots
                .Include(x => x.VehicleAdvert)
            .ThenInclude(x => x.RetailerSite)
            .Where(x =>
                    x.SnapshotDate.Date == runDay.Date &&
                    x.VehicleAdvert.RetailerSite.DealerGroup_Id == (int)dealerGroup &&
                    x.LifecycleStatus != "WASTEBIN" &&
                    x.LifecycleStatus != "SOLD"
                    )
            .ToListAsync();

            var result = vehicleAdvertSnapshots
                .GroupBy(x => x.VehicleAdvert_Id)
                .Select(g => g.OrderByDescending(x => x.Id).First())
                .ToList();

            return result;
         }
      }
      public async Task SaveNewSnapshots(List<VehicleAdvertSnapshot> newSnapshots)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.VehicleAdvertSnapshots.AddRange(newSnapshots);
            await db.SaveChangesAsync();

            // Update each advert with the latest snapshotId
            var adIds = newSnapshots.Select(x => x.VehicleAdvert_Id).ToList();
            await UpdateLatestVehicleAdvertSnapshotIdsAsync(db, adIds);
         }
      }


      public async Task UpdateLatestVehicleAdvertSnapshotIdsAsync(
          CPHIDbContext db,
          List<int> vehicleAdvertIds)
      {
         // If there's nothing to update, just return
         if (vehicleAdvertIds == null || vehicleAdvertIds.Count == 0)
         {
            return;
         }

         // Build the comma-separated list of IDs
         var idList = string.Join(",", vehicleAdvertIds);

         // Construct the raw SQL
         var sql = $@"
        UPDATE va
        SET va.LatestVehicleAdvertSnapshotId = sub.MaxSnapshotId
        FROM autoprice.VehicleAdverts va
        INNER JOIN 
        (
            SELECT VehicleAdvert_Id, MAX(Id) AS MaxSnapshotId
            FROM autoprice.VehicleAdvertSnapshots
            GROUP BY VehicleAdvert_Id
        ) sub
            ON va.Id = sub.VehicleAdvert_Id
        WHERE va.Id IN ({idList});
    ";

         // Execute the statement
         await db.Database.ExecuteSqlRawAsync(sql);
      }


      public async Task SaveUpdatedStrategyForSnapshots(List<VehicleAdvertWithRating> adverts, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDict)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            //update the underlying snapshots table
            var snapshotIds = adverts.Select(x => x.SnapshotId).ToList();
            var advertsBySnapshotId = adverts.ToDictionary(x => x.SnapshotId);
            var dbSnapshots = await db.VehicleAdvertSnapshots.Where(x => snapshotIds.Contains(x.Id)).ToListAsync();

            foreach (var snapshot in dbSnapshots)
            {
               var advert = advertsBySnapshotId[snapshot.Id];
               snapshot.StrategyPrice = advert.StrategyPrice;
               snapshot.StrategyPriceHasBeenCalculated = advert.StrategyPriceHasBeenCalculated;
               snapshot.TestStrategyPrice = advert.TestStrategyPrice;
               snapshot.TestStrategyDaysToSell = advert.TestStrategyDaysToSell;
            }

            await db.SaveChangesAsync();
         }
      }


      public async Task SaveCompetitorInfo(List<VehicleAdvertCompetitorSummary> updatedItems)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Extract the IDs of the vehicle advert snapshots from the items
            var snapshotIds = updatedItems.Select(item => item.VehicleAdvertSnapshotId).ToList();

            // Create a dictionary for fast lookup of items by VehicleAdvertSnapshotId
            var updatedItemsBySnapshotId = updatedItems.Where(x => x.VehicleAdvertSnapshotId != 0).ToDictionary(item => item.VehicleAdvertSnapshotId);

            // Fetch all relevant VehicleAdvertSnapshots in one query
            var dbItems = await db.VehicleAdvertSnapshots
                .Where(x => snapshotIds.Contains(x.Id))
                .ToListAsync();

            // Update the properties of the fetched VehicleAdvertSnapshots
            foreach (var dbItem in dbItems)
            {
               if (updatedItemsBySnapshotId.TryGetValue(dbItem.Id, out var item))
               {
                  dbItem.CompetitorCount = item.CompetitorCount;
                  dbItem.AveragePP = item.AveragePP;
                  dbItem.StDevPP = item.StDevPP;
                  dbItem.HighestPP = item.HighestPP;
                  dbItem.LowestPP = item.LowestPP;
                  dbItem.OurPPRank = item.OurPPRank;
                  dbItem.OurValueRank = item.OurValueRank;
                  dbItem.CheapestSellerName = item.CheapestSellerName;
                  dbItem.CheapestSellerType = item.CheapestSellerType;
                  dbItem.CheapestVehicle = item.CheapestVehicle;
                  dbItem.OnlyVehicle = item.OnlyVehicle;
                  dbItem.AllPrices = item.AllPrices;
                  dbItem.PriceUpMaintainRank = item.PriceUpMaintainRank;
                  dbItem.PriceDownImproveRank = item.PriceDownImproveRank;
                  dbItem.PriceToBeCheapest = item.PriceToBeCheapest;

                  dbItem.PPAverageFranchised = item.PPAverageFranchised;
                  dbItem.PPAverageIndependents = item.PPAverageIndependents;
                  dbItem.PPAverageSupermarkets = item.PPAverageSupermarkets;
                  dbItem.PPAveragePrivates = item.PPAveragePrivates;
               }
            }

            //Now do the same for vehicle advert items
            // Fetch all relevant VehicleAdvertSnapshots in one query
            //var advertItems = await db.VehicleAdvertItems
            //    .Where(x => snapshotIds.Contains(x.VehicleAdvertSnapshot_Id))
            //    .ToListAsync();

            //// Update the properties of the fetched VehicleAdvertSnapshots
            //foreach (var advertItem in advertItems)
            //{
            //    if (updatedItemsBySnapshotId.TryGetValue(advertItem.VehicleAdvertSnapshot_Id, out var item))
            //    {
            //        advertItem.CompetitorCount = item.CompetitorCount;
            //        advertItem.AveragePP = item.AveragePP;
            //        advertItem.HighestPP = item.HighestPP;
            //        advertItem.LowestPP = item.LowestPP;
            //        advertItem.OurPPRank = item.OurPPRank;
            //        advertItem.OurValueRank = item.OurValueRank;
            //        advertItem.CheapestSellerName = item.CheapestSellerName;
            //        advertItem.CheapestSellerType = item.CheapestSellerType;
            //        advertItem.CheapestVehicle = item.CheapestVehicle;
            //        advertItem.OnlyVehicle = item.OnlyVehicle;
            //        advertItem.AllPrices = item.AllPrices;
            //        advertItem.PriceUpMaintainRank = item.PriceUpMaintainRank;
            //        advertItem.PriceDownImproveRank = item.PriceDownImproveRank;
            //        advertItem.PriceToBeCheapest = item.PriceToBeCheapest;
            //    }
            //}



            // Save all changes to the database
            await db.SaveChangesAsync();
         }
      }



      //public async Task SaveUpdatedDaysToSell(List<DaysToSellRequest> adverts)
      public async Task SaveUpdatedDaysToSell(List<DaysToSellRequest> adverts)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var snapshotIds = adverts.Select(x => x.SnapshotId).ToList();
            var advertsBySnapshotId = adverts.ToDictionary(x => x.SnapshotId);

            //update the snapshots
            var dbSnapshots = await db.VehicleAdvertSnapshots.Where(x => snapshotIds.Contains(x.Id)).ToListAsync();
            foreach (var snapshot in dbSnapshots)
            {
               snapshot.DaysToSellAtCurrentSelling = advertsBySnapshotId[snapshot.Id].DaysToSellAtCurrentSelling;
            }

            await db.SaveChangesAsync();
         }
      }



      public async Task SaveFutureValuation(List<FutureValuationPlusValues> futureValuations)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var snapshotIds = futureValuations.Select(x => x.SnapshotId).ToList();
            var futureValuationsBySnapshotId = futureValuations.ToDictionary(x => x.SnapshotId);
            var dbSnapshots = await db.VehicleAdvertSnapshots.Where(x => snapshotIds.Contains(x.Id)).ToListAsync();
            foreach (var snapshot in dbSnapshots)
            {
               snapshot.ValuationMonthPlus1 = futureValuationsBySnapshotId[snapshot.Id].ValuationMonthPlus1;
               snapshot.ValuationMonthPlus2 = futureValuationsBySnapshotId[snapshot.Id].ValuationMonthPlus2;
               snapshot.ValuationMonthPlus3 = futureValuationsBySnapshotId[snapshot.Id].ValuationMonthPlus3;
            }

            await db.SaveChangesAsync();
         }
      }

      public async Task<VehicleAdvertSnapshot> GetVehicleSnapshot(string websiteStockIdentifier)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var advert = await db.VehicleAdverts.FirstAsync(x => x.WebSiteStockIdentifier == websiteStockIdentifier);
            var snapshot = await db.VehicleAdvertSnapshots.Where(x => x.VehicleAdvert_Id == advert.Id).OrderBy(x => x.SnapshotDate).LastAsync();
            return snapshot;
         }
      }

      public async Task SetVehicleSnapshotToNotLatest(int snapshotId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var snapshot = await db.VehicleAdvertSnapshots.Where(x => x.Id == snapshotId).FirstAsync();
            snapshot.IsTodayLatestSnapshot = false;
            await db.SaveChangesAsync();
         }
      }

      public async Task<List<VehicleAdvertSnapshotDateAndPrice>> UpdateDailyPriceMoves(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            try
            {

               int commandTimeout = 300;

               var spParams = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
               return (await dapper.GetAllAsync<VehicleAdvertSnapshotDateAndPrice>("autoprice.UPDATE_DailyPriceMoves", spParams,
                   CommandType.StoredProcedure, null, commandTimeout)).ToList();
            }
            catch (Exception ex)
            {
               throw new Exception(ex.Message, ex);
            }
         }

      }

      public async Task<List<VehicleAdvertSnapshotWithStockNumber>> GetLatestSnapshots(DateTime monthStart, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            try
            {

               int commandTimeout = 300;

               var spParams = new DynamicParameters(new { dealerGroupId = (int)dealerGroup, monthStart = monthStart });

               return (await dapper.GetAllAsync<VehicleAdvertSnapshotWithStockNumber>("autoprice.GET_LatestSnapshots", spParams,
                     CommandType.StoredProcedure, null, commandTimeout)).ToList();
            }
            catch (Exception ex)
            {
               throw new Exception(ex.Message, ex);
            }
         }

      }

      public async Task CreateNewRatingsForUpdatedPrices(IEnumerable<PricingChangeMinimal> okChanges)  
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            //we have made some changes.  In 15 minutes or so autotrader should be updated.
            //rather than waiting to see these latest prices in the ratings we download tomorrow morning
            //we want to go ahead and make them now
            var changesIds = okChanges.Select(x => x.PriceChangeId).ToList();

            var changesWithRatings = await db.PriceChangeAutoItems.Where(x => changesIds.Contains(x.Id))
                                            .Include(x => x.VehicleAdvertSnapshot)
                                            .ToListAsync();

            List<VehicleAdvertSnapshot> newAdvertSnaphots = new List<VehicleAdvertSnapshot>();

            foreach (var change in okChanges)
            {
               var existingRating = changesWithRatings.First(x => x.Id == change.PriceChangeId).VehicleAdvertSnapshot;
               newAdvertSnaphots.Add(new VehicleAdvertSnapshot(existingRating, change.NewPrice));
            }

            await SaveNewSnapshots(newAdvertSnaphots);

            //also we must set the old snapshots to no longer be the latest today snapshot
            foreach (var change in okChanges)
            {
               VehicleAdvertSnapshot existingSnapshot = changesWithRatings.First(x => x.Id == change.PriceChangeId).VehicleAdvertSnapshot;
               existingSnapshot.IsTodayLatestSnapshot = false;
            }

            await db.SaveChangesAsync();
         }
      }
   }
}

