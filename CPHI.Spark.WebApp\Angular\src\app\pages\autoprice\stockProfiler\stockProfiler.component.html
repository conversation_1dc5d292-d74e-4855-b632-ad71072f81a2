<nav class="navbar">
   <nav class="generic">
      <div id="pageTitle">
         <div>Stock Profiler</div>
      </div>

      <ng-container *ngIf="service">
         <div class="buttonGroup topDropdownButtons"></div>

         <div class="buttonGroup">
            <div class="buttonGroup">
               <button
                  class="btn btn-primary"
                  *ngFor="let layout of layoutOptions"
                  [ngClass]="{ active: service.chosenLayout === layout }"
                  (click)="chooseNewLayout(layout)"
               >
                  {{ layout }}
               </button>
            </div>
         </div>
      </ng-container>
   </nav>

   <nav class="pageSpecific"></nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constantsService.environment.customer">
   <div class="content-new">
      <div class="dashboard-grid cols-12">
         <div class="autotrader-tile grid-row-1-6 grid-col-1-13">
            <div>
               <div class="tile-inner">
                  <div class="tile-header"></div>
                  <div class="tile-body">
                     <stockProfilerTable *ngIf="service.stockProfileItems" [tableParams]="service">
                     </stockProfilerTable>
                  </div>
               </div>
            </div>
         </div>

         <br />
      </div>
   </div>
</div>
