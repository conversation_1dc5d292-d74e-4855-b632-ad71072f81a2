﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("VehicleValuationCompetitorAnalysis", Schema = "autoprice")]
   public class VehicleValuationCompetitorAnalysis
   {
      public VehicleValuationCompetitorAnalysis()
      {

      }
      public VehicleValuationCompetitorAnalysis(CompetitorVehicle comp, int VehicleValuationId)
      {
         VehicleValuation_Id = VehicleValuationId;
         WebsiteSearchIdentifier = comp.WebsiteSearchIdentifier;
         ImageURL = comp.ImageURL;
         CompetitorName = comp.CompetitorName;
         Segment = comp.Segment;
         VehicleReg = comp.VehicleReg;
         Year = comp.Year;
         Mileage = comp.Mileage;
         AdvertisedPrice = comp.AdvertisedPrice;
         PricePosition = comp.PricePosition;
         Distance = comp.Distance;
         SeptemberPlateChange = comp.SeptemberPlateChange;
         IsOurVehicle = comp.IsOurVehicle;
         VehicleType = comp.VehicleType;

         FuelType = comp.FuelType;
         BodyType = comp.BodyType;
         Doors = comp.Doors;
         TransmissionType = comp.TransmissionType;
         Trim = comp.Trim;

         Town = comp.Town;
         Region = comp.Region;
         Postcode = comp.Postcode;
      }

      [Key]
      public int Id { get; set; }

      //FKs
      public int VehicleValuation_Id { get; set; }
      [ForeignKey("VehicleValuation_Id")]
      public VehicleValuation VehicleValuation { get; set; }

      public string WebsiteSearchIdentifier { get; set; }
      public string ImageURL { get; set; }
      public string CompetitorName { get; set; }
      public string Segment { get; set; }
      public string VehicleReg { get; set; }
      public int? Year { get; set; }
      public int Mileage { get; set; }
      public int AdvertisedPrice { get; set; }
      public decimal PricePosition { get; set; }
      public int Distance { get; set; }
      public bool? SeptemberPlateChange { get; set; }
      public bool IsOurVehicle { get; set; }
      public string? VehicleType { get; set; }
      public string FuelType { get; set; }
      public string BodyType { get; set; }
      public string Doors { get; set; }
      public string TransmissionType { get; set; }
      public string Trim { get; set; }

      public string Town { get; set; }
      public string Region { get; set; }
      public string Postcode { get; set; }


   }
}
