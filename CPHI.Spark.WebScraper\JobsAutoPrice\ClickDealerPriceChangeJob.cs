using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.WebScraper.Services;
using OpenQA.Selenium.Support.UI;
using System.Threading;
using CPHI.WebScraper.ViewModel;
using System.Collections.Generic;
using System.Linq;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.Repository;
using SeleniumExtras.WaitHelpers;
using CPHI.Repository;

namespace CPHI.Spark.WebScraper.Jobs
{

   [DisallowConcurrentExecution]
   public class ClickDealerPriceChangeJob : IJob
   {
      // Enum to define the operation type for vehicle processing function
      private enum VehicleProcessOperation
      {
         Update,
         Verify
      }

      private static readonly ILog logger = LogManager.GetLogger(typeof(ClickDealerPriceChangeJob));
      private static IWebDriver _driver;
      private const string homePageLink = "https://myclickdealer.co.uk/dealer_interface_login.php";
      private DealerGroupName dealerGroup;

      public void Execute() { }

      public async Task Execute(IJobExecutionContext context)
      {
         logger.Info("");
         logger.Info("========================= Starting ClickDealerPriceChangeJob =====================================");

         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         // Set dealer group - this would typically be determined by configuration
         dealerGroup = DealerGroupName.LMCOfFarnham;

         try
         {
            logger.Info("Starting ClickDealer price change job...");

            List<ClickDealerPriceChange> vehicles = await GetPriceChangesToProcess(dealerGroup);

            if(vehicles.Count == 0)
            {
               return;
            }

            // Set up Chrome driver
            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("ClickDealerPriceChange", 9250);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

            // Get the list of vehicles that need price updates


            //
            //var vehicles = new List<ClickDealerVehicle>
            //{
            //    // Increase by 1
            //    new ClickDealerVehicle("GJ65HDD", 7714), // should always be 82 for LmcFarnham unless they
            //    new ClickDealerVehicle("WO17MVM", 13792),
            //    new ClickDealerVehicle("VO16OPH", 14943),
            //    new ClickDealerVehicle("LN66AHC", 14251),
            //    new ClickDealerVehicle("LA19WXL", 11834),

            //    // Decrease by 1
            //    new ClickDealerVehicle("SE19OLA", 14322),
            //    new ClickDealerVehicle("KE65MBO", 16480),
            //    new ClickDealerVehicle("HV19CZP", 14247),
            //    new ClickDealerVehicle("Y13CEY", 8564),
            //    new ClickDealerVehicle("LC62DFN", 8561),
            //};

            // Login to ClickDealer
            bool loginSuccessful = await LoginAsync();

            if (loginSuccessful)
            {
               logger.Info($"Login OK, continuing with {vehicles.Count} vehicles to update...");

               // Update prices
               UpdateClickDealer(vehicles);

               // Verify changes
               VerifyAllChanges(vehicles);
            }
            else
            {
               logger.Info($"Failed to login, quitting...");
            }

            // Clean up
            _driver.Quit();
            _driver.Dispose();
            stopwatch.Stop();

            logger.Info("========================= Completed ClickDealerPriceChangeJob =====================================");
         }
         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            logger.Error($"Problem with ClickDealer price change job: {e}");
            EmailerService eService = new EmailerService();
            await eService.SendMail($"❌ FAILURE {this.GetType().Name}", $"{e.StackTrace}");
            throw;
         }
      }

      public async Task<List<ClickDealerPriceChange>> GetPriceChangesToProcess(DealerGroupName dealerGroup)
      {
         PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

         bool includeUnPublished = retailers.First().IncludeUnPublishedAds;
         GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup);
         var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         // There are some changes, so now re-run the vehicle opt-out updater
         var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

         // Run again in case we just made some optouts
         GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         string _connectionString = ConfigService.GetConnectionString(dealerGroup);
         var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);

         List<ClickDealerPriceChange> result = todayChangesResponse.totalChanges.ConvertAll(x => new ClickDealerPriceChange(x));
         return result;
      }

      private async Task<bool> LoginAsync()
      {
         logger.Info("Attempting to log in to ClickDealer...");

         try
         {
            // Navigate to login page
            _driver.Navigate().GoToUrl(homePageLink);

            try
            {
               // Wait for username field and enter username
               IWebElement usernameField = WaitAndFind("//input[@id='username']");
               usernameField.SendKeys("Johnf@the19fam"); // Replace with actual username from config
            }
            catch (Exception ex)
            {
               logger.Warn("Username field not found, checking for alternative indicator...");

               try
               {
                  // Check for alternative element that means login might be skipped
                  bool verifyLoginOnStartup = VerifyLogin();

                  if (verifyLoginOnStartup)
                  {
                     logger.Info("Login appears to be already completed, skipping login step.");
                     return true;
                  }
               }
               catch
               {
                  // If neither element is found, rethrow the original exception
                  logger.Error("Username field and fallback element not found.");
                  throw new Exception("Username field missing and fallback check failed.", ex);
               }
            }

            // Wait for password field and enter password
            IWebElement passwordField = WaitAndFind("//input[@id='password']");
            passwordField.SendKeys(ConfigService.ClickDealerPassword);

            // Click login button
            IWebElement loginButton = WaitAndFind("//button[@type='submit']", true);

            // Wait for login to complete
            Thread.Sleep(3000);

            // Verify login was successful
            bool verifyLogin = VerifyLogin();

            if(verifyLogin)
            {
               logger.Info("Login successful");
               return true;
            }
            else
            {
               logger.Error("Login verification failed - dashboard element not found");
               return false;
            }
         }
         catch (Exception e)
         {
            logger.Error($"Login failed: {e.Message}");
            // TakeScreenshot(_driver, "LoginFailure");
            return false;
         }
      }

      private bool VerifyLogin()
      {
         try
         {
            IWebElement globalSearchBox = WaitAndFind("//span[contains(text(), 'Search the site')]", false);

            if(globalSearchBox != null)
            {
               return true;
            }

            return false;
         }
         catch(Exception ex)
         {
            return false;
         }
      }

      private void UpdateClickDealer(List<ClickDealerPriceChange> vehicles)
      {
         // Call the generic method with Update operation
         ProcessVehicles(vehicles, VehicleProcessOperation.Update, "Update ClickDealer");
      }

      private void VerifyAllChanges(List<ClickDealerPriceChange> vehicles)
      {
         // Call the generic method with Verify operation
         List<ClickDealerPriceChange> amendedVehs = vehicles.Where(x => x.PriceChanged).ToList();

         if (amendedVehs.Count > 0)
         {
            logger.Info($"VerifyAllChanges: {amendedVehs.Count} price changes to verify.");
            ProcessVehicles(amendedVehs, VehicleProcessOperation.Verify, "Verifying Changes ClickDealer");
         }
         else
         {
            logger.Info($"VerifyAllChanges: No price changes to verify.");
         }
      }

      // Generic method to process vehicles for either updating or verifying
      private void ProcessVehicles(List<ClickDealerPriceChange> vehicles, VehicleProcessOperation operation, string operationDescription)
      {
         try
         {
            logger.Info($"Starting {operationDescription}");

            // For verify operation, navigate to home page first
            string targetUrl = "https://myclickdealer.co.uk/work_list.php";

            if (!_driver.Url.Equals(targetUrl, StringComparison.OrdinalIgnoreCase))
            {
               _driver.Navigate().GoToUrl(targetUrl);
            }

            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            DateTime start = DateTime.Now;

            // Group vehicles by retailer site
            var vehiclesBySite = vehicles.ToLookup(x => x.RetailerSiteId);

            // Process each site group
            foreach (IGrouping<int, ClickDealerPriceChange> siteGroup in vehiclesBySite)
            {
               logger.Info($"Processing site {siteGroup.Key} with {siteGroup.Count()} vehicles");

               Thread.Sleep(2000);

               // Process each vehicle in the group
               foreach (ClickDealerPriceChange veh in siteGroup)
               {
                  _driver.Navigate().GoToUrl(targetUrl);

                  bool foundItem = SearchInventory(wait, veh);

                  if (foundItem)
                  {
                     // Perform the appropriate action based on operation type
                     if (operation == VehicleProcessOperation.Update)
                     {
                        logger.Info($"Found item: {veh.Reg}");
                        ClickHighlightedWebPriceLink();
                        AmendPriceAsync(veh);
                     }
                     else // Verify operation
                     {
                        ClickHighlightedWebPriceLink();
                        VerifyPrice(veh);
                     }
                  }
               }
            }
         }
         catch (Exception e)
         {
            logger.Error($"Error in ProcessVehicles: {e.Message}");
            // TakeScreenshot(_driver, "ProcessVehiclesFailure");
         }
      }

      private void ClickHighlightedWebPriceLink()
      {
         try
         {
            var highlightedRow = _driver.FindElement(By.XPath("//tr[contains(@class, 'hoverRowSticky')]"));
            var webPriceLink = highlightedRow.FindElement(By.XPath(".//td[starts-with(@id, 'price_td_')]/a"));

            // Scroll into view to reduce chance of intercept
            ((IJavaScriptExecutor)_driver).ExecuteScript("arguments[0].scrollIntoView(true);", webPriceLink);
            Thread.Sleep(500); // Allow any animation or floating headers to settle

            try
            {
               webPriceLink.Click();
            }
            catch (ElementClickInterceptedException)
            {
               ((IJavaScriptExecutor)_driver).ExecuteScript("arguments[0].click();", webPriceLink);
            }
         }
         catch (NoSuchElementException)
         {
            Console.WriteLine("Highlighted row or reg link not found.");
         }
         catch (Exception ex)
         {
            Console.WriteLine($"Error clicking reg link: {ex.Message}");
         }
      }



      private bool SearchInventory(WebDriverWait wait, ClickDealerPriceChange veh)
      {
         try
         {
            logger.Info($"Searching for vehicle: {veh.Reg}");

            // Find the search box
            IWebElement searchBox = wait.Until(ExpectedConditions.ElementExists(By.Id("scroll_link_search")));

            // Clear search box
            searchBox.Clear();
            Thread.Sleep(500);

            // Enter registration and search
            searchBox.SendKeys(veh.Reg);

            Thread.Sleep(500);

            // Have to click button; enter does not work
            WaitAndFind("//a[@id='scroll_button']", true);

            // Wait for search results
            Thread.Sleep(2000);

            if (HasStickyRow(_driver))
            {
               Console.WriteLine("Sticky row found — taking action.");
               return true;
            }
            else
            {
               logger.Info($"Vehicle not found: {veh.Reg}");
               return false;
            }
         }
         catch (Exception e)
         {
            logger.Error($"Error searching for vehicle {veh.Reg}: {e.Message}");
            return false;
         }
      }

      private bool HasStickyRow(IWebDriver driver)
      {
         try
         {
            return driver.FindElements(By.XPath("//tr[contains(@class, 'hoverRowSticky')]")).Any();
         }
         catch (Exception ex)
         {
            // Optionally log the error
            Console.WriteLine($"Error checking for sticky row: {ex.Message}");
            return false;
         }
      }


      private async Task AmendPriceAsync(ClickDealerPriceChange veh)
      {
         logger.Info($"Amending price for: {veh.Reg}");

         try
         {
            Thread.Sleep(2000);

            // Get the current price for logging
            string currentPriceStr = GetRetailPriceInputValue();
            int currentPrice = (int) decimal.Parse(currentPriceStr, System.Globalization.CultureInfo.InvariantCulture);

            // Amend the price to the new price
            int newPrice = AmendRetailPriceInput(_driver, veh.Price, veh.ClickDealerFee);

            Thread.Sleep(1000);

            // Click Update Price - confirm test deals
            WaitAndFind("//a[contains(@onclick, 'save_pricePop(') and contains(text(), 'Save and update')]", true);

            Thread.Sleep(1000);

            logger.Info($"Price updated for {veh.Reg} from {currentPrice} to {newPrice}");
            veh.PriceChanged = true;
            veh.SavedOk = true;
         }
         catch (Exception e)
         {
            logger.Error($"Error amending price for {veh.Reg}: {e.Message}");
            veh.SavedOk = false;
            veh.SaveError = e.Message;
         }

         // Save the result to database
         try
         {
            await SaveChangeResult(veh);
            logger.Info($"Saved price change result for {veh.Reg}");
         }
         catch (Exception ex)
         {
            logger.Error($"Failed to save change result for {veh.Reg}: {ex.Message}");
         }
      }

      private string GetRetailPriceInputValue()
      {
         try
         {
            var priceInput = _driver.FindElement(By.XPath("//input[@id='update_web']"));
            return priceInput.GetAttribute("value");
         }
         catch (NoSuchElementException)
         {
            Console.WriteLine("Retail price input not found.");
            return null;
         }
         catch (Exception ex)
         {
            Console.WriteLine($"Error retrieving retail price: {ex.Message}");
            return null;
         }
      }

      private int AmendRetailPriceInput(IWebDriver driver, int newPrice, int? clickDealerFee)
      {
         try
         {
            // Add fees from configuration
            int autoTraderFee = int.Parse(ConfigService.ClickDealerAutoTraderFee);

            newPrice += autoTraderFee; // Add AutoTrader fee
            newPrice += (clickDealerFee != null ? (int) clickDealerFee : 0); // Add ClickDealer fee

            var priceInput = driver.FindElement(By.XPath("//input[@id='update_web']"));
            priceInput.Clear();
            priceInput.SendKeys(newPrice.ToString());

            return newPrice;
         }
         catch (NoSuchElementException)
         {
            Console.WriteLine("Retail price input not found.");
            return newPrice;
         }
         catch (Exception ex)
         {
            Console.WriteLine($"Error updating retail price: {ex.Message}");
            return newPrice;
         }
      }

      private void VerifyPrice(ClickDealerPriceChange veh)
      {
         logger.Info($"Verifying price for: {veh.Reg}");

         try
         {
            Thread.Sleep(2000);

            // Get the current price from the input field
            string currentPriceStr = GetRetailPriceInputValue();
            int currentPrice = (int)decimal.Parse(currentPriceStr, System.Globalization.CultureInfo.InvariantCulture);

            if (currentPrice == veh.Price)
            {
               logger.Info($"Price verification successful for {veh.Reg}: {currentPrice}");
            }
            else
            {
               logger.Error($"Price verification failed for {veh.Reg}. Expected: {veh.Price}, Actual: {currentPrice}");
            }
         }
         catch (Exception e)
         {
            logger.Error($"Error verifying price for {veh.Reg}: {e.Message}");
         }
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "ClickDealerPriceChange", findXPath, andClick);
         return result;
      }

      private static async Task SaveChangeResult(ClickDealerPriceChange change)
      {
         using (var db = new CPHIDbContext(ConfigService.GetConnectionString(DealerGroupName.LMCOfFarnham)))
         {
            if (change.IsAutoPriceChange)
            {
               var dbItem = db.PriceChangeAutoItems.FirstOrDefault(x => x.Id == change.PriceChangeId);
               if (dbItem == null)
               {
                  throw new Exception("Db item not found, are you running in debug with dummy items?");
               }
               if (change.SavedOk)
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.DateConfirmed = DateTime.UtcNow;
               }
               else
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.SaveResult = change.SaveError;
               }
            }
            else
            {
               var dbItem = db.PriceChangeManualItems.FirstOrDefault(x => x.Id == change.PriceChangeId);

               if (dbItem == null)
               {
                  throw new Exception("Db item not found, are you running in debug with dummy items?");
               }
               if (change.SavedOk)
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.DateConfirmed = DateTime.UtcNow;
               }
               else
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.SaveResult = change.SaveError;
               }
            }

            await db.SaveChangesAsync();
         }
      }

      //private void TakeScreenshot(IWebDriver driver, string screenshotName)
      //{
      //   try
      //   {
      //      string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
      //      string screenshotPath = Path.Combine(ConfigService.FileDownloadLocation, $"{timestamp}_{screenshotName}.png");

      //      Screenshot screenshot = ((ITakesScreenshot)driver).GetScreenshot();
      //      screenshot.SaveAsFile(screenshotPath, ScreenshotImageFormat.Png);

      //      logger.Info($"Screenshot saved to {screenshotPath}");
      //   }
      //   catch (Exception e)
      //   {
      //      logger.Error($"Failed to take screenshot: {e.Message}");
      //   }
      //}
   }
}
