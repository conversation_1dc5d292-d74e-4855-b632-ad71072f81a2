﻿using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using System;

public class LeavingVehicleModelItem
{
    public int RetailerSiteId { get; set; }
    public string RetailerSiteName { get; set; }
    public string RetailerMakes { get; set; }
    public string Make { get; set; }
    public string Model { get; set; }
    public int SuppliedPrice { get; set; }
    public DateTime FirstRegisteredDate { get; set; }
    public string Drivetrain { get; set; }
    public string FuelType { get; set; }
    public string BodyType { get; set; }
    public string VehicleReg { get; set; }
    public int? Valuation { get; set; }
    public DateTime FirstSnapshotDate { get; set; }
    public DateTime LastSnapshotDate { get; set; }
    public int? PricedProfit { get; set; }

    //public string AgeDaysListedFrom { get; set; }
    public int DaysListed
    {
        get
        {
            return (LastSnapshotDate - FirstSnapshotDate).Days;
        }
    }

    public string ModelCleanedUp { get => AdvertStratifierService.StandardiseModelName(Model,RetailerMakes,Make); }


    public string AgeBand { get => BandingsService.ProvideAgeBand((DateTime.Now - (DateTime)(FirstRegisteredDate)).Days / 365M); }
    public string DaysListedBand { get => BandingsService.ProvideDaysBanding(DaysListed); }
    public string ValueBand { get => BandingsService.ProvideValueBanding(SuppliedPrice); }
}

public class LeavingVehicleModelItemClean
{
   public int RetailerSiteId { get; set; }
   public string RetailerSiteName { get; set; }
   public string RetailerMakes { get; set; }
   public string Make { get; set; }
   public string Drivetrain { get; set; }
   public string FuelType { get; set; }
   public string BodyType { get; set; }
   public string AgeBand { get; set; }
   public string DaysListedBand { get; set; }
   public string ValueBand { get; set; }

   public int MonthlySalesRate { get; set; } // count
   public int TotalPricedProfit { get; set; }
   public int TotalDaysListed { get; set; }
}
