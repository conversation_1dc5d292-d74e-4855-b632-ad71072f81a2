export interface PricingChangeNew {
    RetailerName: string;
    RetailerSiteId: number;
    WhenToActionChangesEachDay?: Date | string;
    VehicleReg: string;
    Make: string;
    Model: string;
    Derivative: string;
    WebsiteStockIdentifier: string;
    WebSiteSearchIdentifier: string;
    AdvertId: number;
    DaysListed: number;
    DaysInStock: number;
    PriceChangeId?: number;
    SpecificColour: string;
    FirstRegisteredDate: Date | string | null;
    Owners: string;
    AgeAndOwners: string;
    RetailerStockType: string;

    WasPrice: number;
    PriceIndicatorRatingAtCurrentSelling: string;
    DaysToSellAtCurrentSelling: number;
    ValuationMktAvRetail: number;
    ValuationAdjustedRetail: number;
    StrategyPrice: number;
    WasPriceVsStrategyBanding: string;
    RetailRating: number;
    NewPrice: number;
    NewDaysToSell: number;
    NewPriceIndicatorRating?: string;
    DaysToSellChange: number;
    IsOptedOutOnDay: boolean;

    OwnershipCondition: string;
    LastComment: string;
    DateConfirmed: Date | string | null;
    IsSmallChange: boolean;
    IsIncrease: boolean;

    TotalChangeValue: number;
    TotalChangePercent: number;
    Status: string;

    ApprovedDate: Date | string | null;
    ApprovedById: number | null;
    ApproverHasBeenEmailed: boolean;

    ChangeValueUp: number;
    ChangePercentUp: number;
    ChangeValueDown: number;
    ChangePercentDown: number;

    RetailerAdminFee: number;
    NewPriceExclAdminFee: number;

    VehicleTypeDesc: string;
    OurValueRank: number;
    OurPPRank: number;

    StockNumber: string;
    IsSetToAutoUpdatePrice: boolean;

    IsKeyChange: boolean;
    PrevAppearances: number;
    MostRecentDailyPriceMove: number;
    MostRecentDailyPriceMoveDate: Date | string | null;

    LifecycleStatus: string;
    ClickDealerFee: number | null;
    PerformanceRatingScore: number | null;
}
