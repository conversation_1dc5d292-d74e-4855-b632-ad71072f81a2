import { Input, Output, HostListener, OnInit, Component, EventEmitter, OnDestroy } from "@angular/core";
import { GridApi } from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExportService";
import { SelectionsService } from "src/app/services/selections.service";
import { BarComponent } from "src/app/_cellRenderers/bar.component";
import { GDPRService } from "./gdpr.service";
import { GDPRRow } from './GDPRRow';
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
  selector: 'gdprTable',
  template: `
    <div class="position-relative">
      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"></ag-grid-angular>
    </div>
  `
  ,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `
    ag-grid-angular { width: 100%; }

    `
  ]
})

export class GDPRTableComponent implements OnInit,OnDestroy {
  private destroy$ = new Subject<void>();
  @Input() sites?: boolean;
  @Input() regional?: boolean;
  @Input() managers?: boolean;
  @Output() clickedSite = new EventEmitter<GDPRRow>();

  @HostListener("window:resize", [])

  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if(this.gridApi){
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  mainTableGridOptions: GridOptionsCph;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public service: GDPRService,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService
  ) { }

  ngOnInit(): void {
    this.setGridOptions();
    
    this.service.gdpr.sitesDataChangedEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      if (this.gridApi) {
        let rowData: GDPRRow[] = this.service.gdpr.sitesData;
        this.gridApi.setRowData(this.regional ? rowData.filter(x => x.IsRegion) : rowData.filter(x => x.IsSite));
        this.gridApi.setPinnedBottomRowData(rowData.filter(x => x.IsTotal));
      }
    })

    this.service.gdpr.peopleDataChangedEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      if (this.gridApi) {
        this.gridApi.setRowData(this.service.gdpr.peopleData.filter(x => !x.IsTotal));
        this.gridApi.setPinnedBottomRowData(this.service.gdpr.peopleData.filter(x => x.IsTotal));
      }
    })
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
}

  setGridOptions() {
    let rowData: GDPRRow[];
    let pinnedBottomRowData: GDPRRow[];

    if (this.sites) {
      if (this.regional) {
        rowData = this.service.gdpr.sitesData.filter(x => x.IsRegion);
      } else {
        rowData = this.service.gdpr.sitesData.filter(x => x.IsSite);
      }
      pinnedBottomRowData = this.service.gdpr.sitesData.filter(x => x.IsTotal);
    } else {
      rowData = this.service.gdpr.peopleData.filter(x => !x.IsTotal);
      pinnedBottomRowData = this.service.gdpr.peopleData.filter(x => x.IsTotal);
    }

    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      // getLocaleText: (key: string, defaultValue: string) => {
      //   this.constants.currentLang == 'es' ? localeEs[key] || defaultValue : defaultValue;
      // },
      onCellClicked: (params) => {
        this.onCellClick(params.data);
      },
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onGridReady: (params) => {
        this.onGridReady(params);
      },
      
      rowData: rowData,
      pinnedBottomRowData: pinnedBottomRowData,
      domLayout: 'autoHeight',
      defaultColDef: {
        resizable: true,
        sortable: true,
        autoHeight: true
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      columnDefs: [
        { headerName: '', colId: 'Label', field: 'Label', width: 150, type: 'label' },
        { headerName: 'Contacts', colId: 'TotalEntries', field: 'TotalEntries', width: 80, type: 'number' },
        {
          headerName: 'Vindis', children: [
            { headerName: 'Email #', colId: 'Email', field: 'Email', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'Email %', colId: 'EmailPercentage', field: 'EmailPercentage', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds },
            { headerName: 'Phone #', colId: 'Phone', field: 'Phone', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'Phone %', colId: 'PhonePercentage', field: 'PhonePercentage', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds },
            { headerName: 'SMS #', colId: 'SMS', field: 'SMS', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'SMS %', colId: 'SMSPercentage', field: 'SMSPercentage', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds },
            { headerName: 'Post #', colId: 'Post', field: 'Post', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'Post %', colId: 'PostPercentage', field: 'PostPercentage', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds }
          ]
        },
        {
          headerName: 'Brand', children: [
            { headerName: 'Email #', colId: 'EmailBrand', field: 'EmailBrand', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'Email %', colId: 'EmailPercentageBrand', field: 'EmailPercentageBrand', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds },
            { headerName: 'Phone #', colId: 'PhoneBrand', field: 'PhoneBrand', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'Phone %', colId: 'PhonePercentageBrand', field: 'PhonePercentageBrand', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds },
            { headerName: 'SMS #', colId: 'SMSBrand', field: 'SMSBrand', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'SMS %', colId: 'SMSPercentageBrand', field: 'SMSPercentageBrand', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds },
            { headerName: 'Post #', colId: 'PostBrand', field: 'PostBrand', width: 80, type: 'number', cellClass: 'cell-border-left ag-right-aligned-cell' },
            { headerName: 'Post %', colId: 'PostPercentageBrand', field: 'PostPercentageBrand', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.gdpr.barThresholds }
          ]
        }
      ]
    }
  }

  onCellClick(params: GDPRRow) {
    if (this.sites || this.managers) this.clickedSite.emit(params);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
  }

  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  excelExport() {
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'GDPR Capture', 1, 1);
  }
}
