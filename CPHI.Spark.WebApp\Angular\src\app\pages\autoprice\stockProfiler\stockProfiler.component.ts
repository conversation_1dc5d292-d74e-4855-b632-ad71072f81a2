
import { Component, OnInit } from '@angular/core';
import {   ChosenStockProfilerLayout, StockProfilerService } from './stockProfiler.service';

export enum StockProfilerPageComponentType {
   grid, page
}

export enum StockProfilerTableColumnChoices{
  DaysListed,RetailRating,PerformanceIndicator
}



@Component({
  selector: 'stockProfiler',
  templateUrl: './stockProfiler.component.html',
  styleUrls: ['./stockProfiler.component.scss']
})


export class StockProfilerComponent implements OnInit {
  indicateNewData: boolean;

ChosenStockProfilerLayout = ChosenStockProfilerLayout;
layoutOptions:ChosenStockProfilerLayout[] = Object.values(ChosenStockProfilerLayout);

  constructor(
    public service: StockProfilerService,

  ) {


  }

  // get countChosenItems() {
  //   return this.service.simpleExampleItemsFiltered.filter(x => x.isChosen).length;
  // }




  async ngOnInit() {

    this.service.initParams();
    this.service.pageRef=this;
    await this.service.getData();

  }



  ngOnDestroy() {

  }

  

  public async onChosenSitesChange() {
    await this.service.getData();
  }




  dealWithNewData(data: any): void {
    //nothing
    this.indicateNewData = true;
    setTimeout(() => { this.indicateNewData = false; }, 1000);
  }


 chooseNewLayout(layout: ChosenStockProfilerLayout){
  this.service.chosenLayout = layout;
  this.service.gridRef.updateColumns();
}



}

