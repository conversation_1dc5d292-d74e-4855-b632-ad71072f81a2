﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Globalization;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
   public class CitNowJobVindis : IJob
   {


      private static readonly ILog Logger = LogManager.GetLogger(typeof(CitNowJobVindis));
      private const string fileSearch = "*SPKV36?.csv";
      /*
| lid               | uk/vskobse_s                         | No |
|-------------------|--------------------------------------|----|
| name              | Vindis Å KODA Bury St Edmunds        | No |
| code              | -                                    | No |
| region            | -                                    | No |
| sid               | 52818386                             | No |
| salesexec         | <PERSON>                       | No |
| email             | <PERSON>.<PERSON>@Vindis.com            | No |
| created           | 45007                                | No |
| reg               | av20edl                              | No |
| uploadedDuration  | 195                                  | No |
| videoStatus       | normal                               | No |
| emailDate         | 45007.39309                          | No |
| dateSMS           | 45007                                | No |
| duration          | 195                                  | No |
| served_at         | 45007                                | No |
| played_at         | 45007                                | No |
| timesServed       | 9                                    | No |
| timesPlayed       | 3                                    | No |
| PercentageWatched | 94                                   | No |
| timeToView        | 0.017361111                          | No |
| rating            | -                                    | No |
| video_purpose     | Enquiry                              | No |
| url               | https://video.citnow.com/vrFLzn-SKNK | No |

*/

      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();


         string[] filePaths = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot, fileSearch));

         //check for presence of file, if so, return as already running
         if (LocksService.VindisCitNow) { CentralLoggingService.ReportLock("CitNowJob"); return; }

         if (filePaths.Length == 0)
         {
            //nothing found
            TimeSpan age = DateTime.UtcNow - PulsesService.CitNow;
            if (age.Minutes > 120)
            {
               PulsesService.CitNow = DateTime.UtcNow;
               Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPKV36?.csv");
            }
            return;
         }


         //define Lists
         List<CitNowItem> dbCitNowItems;
         List<Site> dbSites;
         List<Person> dbPeople;
         LocksService.VindisCitNow = true;

         if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
         {
            System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
         }

         DateTime start = DateTime.UtcNow;

         using (var db = new CPHIDbContext())

         {
            int errorCount = 0;
            LogMessage logMessage = new LogMessage();
            logMessage.DealerGroup_Id = 3;

            try
            {


               dbSites = db.Sites.ToList();
               dbPeople = db.People.ToList();
               dbCitNowItems = db.CitNowItems.ToList();


               logMessage.SourceDate = DateTime.UtcNow;
               logMessage.Job = this.GetType().Name;

               Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found


               //go through first file
               string filePath = filePaths[0];


               //define variables for use in processing this file
               int incomingCount = 0;
               int removedCount = 0;
               int newCount = 0;
               int changedCount = 0;
               //bool sales;

               if (File.Exists(filePath.Replace(".csv", "-p.csv")))
               {
                  //already processing a file of this type, skip
                  Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                  logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
               }
               File.Move(filePath, filePath.Replace(".csv", "-p.csv")); //append _p to the file to prevent any other instances also processing these files
               var newFilepath = filePath.Replace(".csv", "-p.csv");

               string fileName = Path.GetFileName(filePath);
               var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
               logMessage.SourceDate = fileDate;

               Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

               List<CitNowItem> incomingCitNow = new List<CitNowItem>(10000);  //preset the list size (slightly quicker than growing it each time)

               var allText = File.ReadAllText(newFilepath);

               // Remove any commas within user input fields as these cause issues
               // Regex removes any commas within fields surrounded by "
               var regex = new Regex("\\\"(.*?)\\\"");

               string allTextCommasRemoved = regex.Replace(allText, m => m.Value.Replace(',', '_'));
               string allTextNoCR = allTextCommasRemoved.Replace("\r", "").Replace("\"", "");

               var rows = allTextNoCR.Split('\n');

               var headers = rows.Skip(0).First().ToUpper().Split(',');

               foreach (var row in rows.Skip(1))
               {
                  if (!string.IsNullOrEmpty(row))
                  {
                     incomingCount++;


                     try
                     {
                        string rowNoCR = row.Replace("\r\n", "");

                        var cells = Regex.Matches(rowNoCR, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                            .Cast<Match>()
                            .Select(m => m.Value)
                            .ToArray();

                        if (cells.Length != headers.Length)
                        {
                           //something weird happened, not got enough cells for the number of headerCols, skip this record
                           logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {cells.Length} cells and needed {headers.Length} file is {filePath}";
                           errorCount++;
                           continue;
                        }

                        //lookup objects required

                        //site
                        string siteName = cells[Array.IndexOf(headers, "NAME")];
                        string citNowName = cells[Array.IndexOf(headers, "SALESEXEC")];
                        int siteId = 0;

                        //department
                        bool isSales = false;

                        //people
                        //string exec = cells[Array.IndexOf(headers, "EMAIL")]; //update this to people table once table updated
                        int? execId = null;

                        try
                        {
                           Person exec = dbPeople.First(x => x.CitNowName == citNowName);
                           execId = exec.Id;
                        }
                        catch { };

                        //isDeleted
                        bool isDeleted = cells[Array.IndexOf(headers, "VIDEOSTATUS")] == "deleted";

                        //seconds watched
                        int secondsWatched = 0;
                        try { secondsWatched = Convert.ToInt32(TimeSpan.Parse(cells[Array.IndexOf(headers, "TIMETOVIEW")]).TotalMinutes); } catch { };

                        //rating
                        int rating = 0;
                        try { rating = int.Parse(cells[Array.IndexOf(headers, "RATING")]); } catch { }

                        //timesServed
                        int timesServed = 0;
                        try { timesServed = int.Parse(cells[Array.IndexOf(headers, "TIMESSERVED")]); } catch { }

                        //timesPlayed
                        int timesPlayed = 0;
                        try { timesPlayed = int.Parse(cells[Array.IndexOf(headers, "TIMESPLAYED")]); } catch { }

                        //percentageWatched
                        int percentageWatched = 0;
                        try { percentageWatched = int.Parse(cells[Array.IndexOf(headers, "PERCENTAGEWATCHED")]); } catch { }

                        //duration
                        int duration = 0;
                        try { duration = int.Parse(cells[Array.IndexOf(headers, "UPLOADEDDURATION")]); } catch { }

                        //sid
                        int sid = 0;
                        try { sid = int.Parse(cells[Array.IndexOf(headers, "SID")]); } catch { }
                        if (sid == 0) continue;

                        //skip empty rows
                        if (cells[Array.IndexOf(headers, "CREATED")] == "-") { continue; }

                        //dates
                        DateTime? dateCreated = DateTime.Today;
                        DateTime? dateEmailed = null;
                        DateTime? dateTexted = null;
                        DateTime? dateServed = null;
                        DateTime? datePlayed = null;

                        try { dateCreated = DateTime.ParseExact(cells[Array.IndexOf(headers, "CREATED")], "yyyy-MM-dd", null); } catch { continue; } //skip blanks
                        try { dateEmailed = DateTime.ParseExact(cells[Array.IndexOf(headers, "EMAILDATE")], "yyyy-MM-dd HH:mm:ss", null); } catch { }
                        try { dateTexted = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATESMS")], "yyyy-MM-dd", null); } catch { }
                        try { dateServed = DateTime.ParseExact(cells[Array.IndexOf(headers, "SERVED_AT")], "yyyy-MM-dd", null); } catch { }
                        try { datePlayed = DateTime.ParseExact(cells[Array.IndexOf(headers, "PLAYED_AT")], "yyyy-MM-dd", null); } catch { datePlayed = null; } //skip ones not played

                        if (fileName.Contains("SPKV36a")) isSales = true;

                        //branch & department
                        switch (siteName)
                        {
                           case "Vindis Northampton Audi": { siteId = 4; break; }
                           case "Vindis Bedford Volkswagen": { siteId = 6; break; }
                           case "Vindis Volkswagen Cambridge": { siteId = 7; break; }
                           case "Vindis Peterborough Audi": { siteId = 5; break; }
                           case "Peterborough Audi": { siteId = 5; break; }
                           case "Bentley Cambridge": { siteId = 16; break; }
                           case "Three10 Automotive": { siteId = 16; break; }
                           case "Three 10 Automotive": { siteId = 16; break; }
                           case "Three 10 Automotive Bentley Cambridge": { siteId = 16; break; }
                           case "Ducati Cambridge ": { siteId = 15; break; }
                           case "Vindis Van Centre Northampton Sales": { siteId = 11; break; }
                           case "Vindis Volkswagen Huntingdon": { siteId = 9; break; }
                           case "Vindis Cambridge Audi": { siteId = 2; break; }
                           case "Cambridge Audi": { siteId = 2; break; }
                           case "Vindis Volkswagen Huntingdon Fleet": { siteId = 9; break; }
                           case "Vindis Huntingdon Audi": { siteId = 3; break; }
                           case "Vindis Bedford Audi": { siteId = 1; break; }
                           case "Cookes of Fakenham Volkswagen": { siteId = 8; break; }
                           case "Vindis ŠKODA Cambridge": { siteId = 13; break; }
                           case "Vindis Cambridge": { siteId = 13; break; }
                           case "Vindis ŠKODA Bury St Edmunds": { siteId = 12; break; }
                           case "Vindis SEAT Milton Keynes": { siteId = 14; break; }
                           case "Vindis Volkswagen Van Centre Northampton Fleet": { siteId = 11; break; }
                           case "Vindis Audi Fleet": { siteId = 22; break; }
                           case "Vindis Volkswagen Bedford Fleet": { siteId = 6; break; }
                           case "Vindis Volkswagen Cambridge Fleet": { siteId = 7; break; }
                           case "Vindis Volkswagen Bedford Workshop": { siteId = 6; break; }
                           case "Cookes of Fakenham Volkswagen Workshop": { siteId = 8; break; }
                           case "Vindis Volkswagen Huntingdon Workshop": { siteId = 9; break; }
                           case "Vindis Van Centre Northampton Workshop": { siteId = 11; break; }
                           case "Vindis Van Centre Huntingdon Workshop": { siteId = 10; break; }
                           case "Vindis Milton Keynes": { siteId = 14; break; }
                        }

                        if (siteId == 0) { continue; } //if no matching siteId found then skip this row

                        CitNowItem e = new CitNowItem(); //initialise new one
                        e.IsSales = isSales;
                        e.Person_Id = execId;
                        e.Sid = sid;
                        e.SalesExec = cells[Array.IndexOf(headers, "SALESEXEC")];
                        e.DateCreated = (DateTime)dateCreated;
                        e.Reg = cells[Array.IndexOf(headers, "REG")].ToUpper();
                        e.Duration = duration;
                        e.DateEmailed = dateEmailed;
                        e.DateTexted = dateTexted;
                        e.DateServed = dateServed;
                        e.DatePlayed = datePlayed;
                        e.TimesServed = timesServed;
                        e.TimesPlayed = timesPlayed;
                        e.PercentWatched = percentageWatched;
                        e.TimeToView = secondsWatched;
                        e.Rating = rating;
                        e.Purpose = "";
                        e.Url = cells[Array.IndexOf(headers, "URL")];
                        e.Site_Id = siteId;
                        e.IsDeleted = isDeleted;

                        if (e.Reg.Length > 50)
                        {
                           e.Reg = e.Reg.Substring(0, 50);
                        }


                        incomingCitNow.Add(e);

                        Console.WriteLine(incomingCount);

                     }

                     catch (Exception err)
                     {
                        if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, CitNow: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                        errorCount++;
                        continue;
                     }

                  }
               }

               List<CitNowItem> newItems = new List<CitNowItem>();
               foreach (var line in incomingCitNow)
               {
                  bool lineHasChanged = false;
                  CitNowItem matchingCode = dbCitNowItems.Find(x => x.Sid == line.Sid);
                  if (matchingCode == null) { newItems.Add(line); continue; }
                  if (matchingCode.IsSales != line.IsSales) { matchingCode.IsSales = line.IsSales; lineHasChanged = true; };
                  if (matchingCode.Person_Id != line.Person_Id) { matchingCode.Person_Id = line.Person_Id; lineHasChanged = true; };
                  if (matchingCode.Sid != line.Sid) { matchingCode.Sid = line.Sid; lineHasChanged = true; };
                  if (matchingCode.SalesExec != line.SalesExec) { matchingCode.SalesExec = line.SalesExec; lineHasChanged = true; };
                  if (matchingCode.DateCreated != line.DateCreated) { matchingCode.DateCreated = line.DateCreated; lineHasChanged = true; };
                  if (matchingCode.Reg != line.Reg) { matchingCode.Reg = line.Reg; lineHasChanged = true; };
                  if (matchingCode.Duration != line.Duration) { matchingCode.Duration = line.Duration; lineHasChanged = true; };
                  if (matchingCode.DateEmailed != line.DateEmailed) { matchingCode.DateEmailed = line.DateEmailed; lineHasChanged = true; };
                  if (matchingCode.DateTexted != line.DateTexted) { matchingCode.DateTexted = line.DateTexted; lineHasChanged = true; };
                  if (matchingCode.DateServed != line.DateServed) { matchingCode.DateServed = line.DateServed; lineHasChanged = true; };
                  if (matchingCode.DatePlayed != line.DatePlayed) { matchingCode.DatePlayed = line.DatePlayed; lineHasChanged = true; };
                  if (matchingCode.TimesServed != line.TimesServed) { matchingCode.TimesServed = line.TimesServed; lineHasChanged = true; };
                  if (matchingCode.TimesPlayed != line.TimesPlayed) { matchingCode.TimesPlayed = line.TimesPlayed; lineHasChanged = true; };
                  if (matchingCode.PercentWatched != line.PercentWatched) { matchingCode.PercentWatched = line.PercentWatched; lineHasChanged = true; };
                  if (matchingCode.TimeToView != line.TimeToView) { matchingCode.TimeToView = line.TimeToView; lineHasChanged = true; };
                  if (matchingCode.Rating != line.Rating) { matchingCode.Rating = line.Rating; lineHasChanged = true; };
                  if (matchingCode.Purpose != line.Purpose) { matchingCode.Purpose = line.Purpose; lineHasChanged = true; };
                  if (matchingCode.Url != line.Url) { matchingCode.Url = line.Url; lineHasChanged = true; };
                  if (matchingCode.Site_Id != line.Site_Id) { matchingCode.Site_Id = line.Site_Id; lineHasChanged = true; };
                  if (matchingCode.IsDeleted != line.IsDeleted) { matchingCode.IsDeleted = line.IsDeleted; lineHasChanged = true; };

                  if (lineHasChanged) changedCount++;
               }
               DateTime finishedInterpetFile = DateTime.UtcNow;
               newCount = newItems.Count;


               try
               {
                  db.CitNowItems.AddRange(newItems);  //add them all in one go
                  db.SaveChanges();
               }
               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                  errorCount++;
               }

               logMessage.FinishDate = DateTime.UtcNow;
               logMessage.ProcessedCount = incomingCitNow.Count;
               logMessage.AddedCount = newCount;
               logMessage.RemovedCount = removedCount;
               logMessage.ChangedCount = changedCount;
               logMessage.IsCompleted = true;
               logMessage.ErrorCount = errorCount;


               Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingCitNow.Count} item(s) interpreted, added {newCount} new, changed {changedCount}");
               try
               {
                  File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                  if (errorCount > 0)
                  {
                     //we have errors so use the reporter
                     logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                     await CentralLoggingService.ReportError("CitNow", logMessage, true);
                     await CentralLoggingService.ReportError("CitNow", logMessage, true);

                  }
                  else
                  {
                     //no errors so just save the log
                     DateTime finishedUpdateDb = DateTime.UtcNow;
                     logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                     logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                     logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                     db.LogMessages.Add(logMessage);
                     db.SaveChanges();

                  }

                  GlobalParam dealLatestsUpdateDate = db.GlobalParams.First(x => x.Description == "citNowUpdateDate");
                  dealLatestsUpdateDate.DateFrom = DateTime.UtcNow; // 
                  dealLatestsUpdateDate.TextValue = DateTime.UtcNow.ToLongDateString();
                  db.SaveChanges();


               }
               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                  errorCount++;

               }

               //trigger cache rebuild
               await UpdateWebAppService.Trigger("CitNowItems");
               stopwatch.Stop();
            }
            catch (Exception err)
            {
               stopwatch.Stop();
               errorMessage = err.ToString();
               await EmailerService.SendErrorMail("Vindis citnow loader failed", err.StackTrace);
               logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
               errorCount++;
               logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;

               await CentralLoggingService.ReportError("CitNow", logMessage);

            }


            finally
            {
               db.ChangeTracker.Clear();
               LocksService.VindisCitNow = false;

               Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
               {
                  Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                  Project = "Loader",
                  Customer = "Vindis",
                  Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                  Task = this.GetType().Name,
                  StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                  EndDate = DateTime.UtcNow,
                  Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                  Notes = errorMessage,
                  HTML = string.Empty
               };
               await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }


         }
      }

      private DateTime? TryParseDate(string stringDate)
      {
         DateTime result;
         bool haveParsed = DateTime.TryParseExact(stringDate, "yyyy-MM-dd", null, DateTimeStyles.None, out result);
         if (haveParsed) { return result; }
         return null;
      }

   }


}
