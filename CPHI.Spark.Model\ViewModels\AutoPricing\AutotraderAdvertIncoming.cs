﻿using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using static CPHI.Spark.Model.ViewModels.AutoPricing.RetailCustomerLatestViewResponseResult;

namespace CPHI.Spark.Model.AutoPricing
{
}
namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class AutotraderAdvertIncoming
   {
      public AutotraderAdvertIncoming(AutoTraderVehicleListing itemIn)
      {
         try
         {
            int totalPrice = itemIn.adverts.retailAdverts.totalPrice.amountGBP ?? 0;

            //Valuations
            if (itemIn.adverts.retailAdverts.vatStatus != null && itemIn.adverts.retailAdverts.vatStatus == "Ex VAT")
            {
               ValuationAdjRetail = itemIn.valuations?.adjusted?.retail?.amountExcludingVatGBP ?? 0;
               ValuationAdjTrade = itemIn.valuations?.adjusted?.trade?.amountExcludingVatGBP ?? 0;
               ValuationAdjPartEx = itemIn.valuations?.adjusted?.partExchange?.amountExcludingVatGBP ?? 0;
               ValuationAdjPrivate = itemIn.valuations?.adjusted?.privateValue?.amountExcludingVatGBP ?? 0;

               ValuationMonthPlus1 = itemIn.trendedValuations?.plus30Days?.adjusted?.retail?.amountExcludingVatGBP;
               ValuationMonthPlus2 = itemIn.trendedValuations?.plus60Days?.adjusted?.retail?.amountExcludingVatGBP;
               ValuationMonthPlus3 = itemIn.trendedValuations?.plus90Days?.adjusted?.retail?.amountExcludingVatGBP;
            }
            else
            {
               ValuationAdjRetail = itemIn.valuations?.adjusted?.retail?.amountGBP ?? 0;
               ValuationAdjTrade = itemIn.valuations?.adjusted?.trade?.amountGBP ?? 0;
               ValuationAdjPartEx = itemIn.valuations?.adjusted?.partExchange?.amountGBP ?? 0;
               ValuationAdjPrivate = itemIn.valuations?.adjusted?.privateValue?.amountGBP ?? 0;

               ValuationMonthPlus1 = itemIn.trendedValuations?.plus30Days?.adjusted?.retail?.amountGBP;
               ValuationMonthPlus2 = itemIn.trendedValuations?.plus60Days?.adjusted?.retail?.amountGBP;
               ValuationMonthPlus3 = itemIn.trendedValuations?.plus90Days?.adjusted?.retail?.amountGBP;
            }

            if (itemIn.adverts.retailAdverts.vatStatus != null && itemIn.adverts.retailAdverts.vatStatus == "Ex VAT")
            {
               ValuationMktAvRetail = itemIn.valuations?.marketAverage?.retail?.amountExcludingVatGBP ?? 0;
               ValuationMktAvTrade = itemIn.valuations?.marketAverage?.trade?.amountExcludingVatGBP ?? 0;
               ValuationMktAvPartEx = itemIn.valuations?.marketAverage?.partExchange?.amountExcludingVatGBP ?? 0;
               ValuationMktAvPrivate = itemIn.valuations?.marketAverage?.privateValue?.amountExcludingVatGBP ?? 0;

               ValuationMonthPlus1 = itemIn.trendedValuations?.plus30Days?.marketAverage?.retail?.amountExcludingVatGBP;
               ValuationMonthPlus2 = itemIn.trendedValuations?.plus60Days?.marketAverage?.retail?.amountExcludingVatGBP;
               ValuationMonthPlus3 = itemIn.trendedValuations?.plus90Days?.marketAverage?.retail?.amountExcludingVatGBP;
            }
            else
            {
               ValuationMktAvRetail = itemIn.valuations?.marketAverage?.retail?.amountGBP ?? 0;
               ValuationMktAvTrade = itemIn.valuations?.marketAverage?.trade?.amountGBP ?? 0;
               ValuationMktAvPartEx = itemIn.valuations?.marketAverage?.partExchange?.amountGBP ?? 0;
               ValuationMktAvPrivate = itemIn.valuations?.marketAverage?.privateValue?.amountGBP ?? 0;

               ValuationMonthPlus1 = itemIn.trendedValuations?.plus30Days?.marketAverage?.retail?.amountGBP;
               ValuationMonthPlus2 = itemIn.trendedValuations?.plus60Days?.marketAverage?.retail?.amountGBP;
               ValuationMonthPlus3 = itemIn.trendedValuations?.plus90Days?.marketAverage?.retail?.amountGBP;
            }





            // Vehicle properties (in property declaration order)
            FirstRegistered = itemIn.vehicle.firstRegistrationDate;
            VehicleType = itemIn.vehicle.vehicleType;
            VehicleReg = itemIn.vehicle.registration;
            Odometer = itemIn.vehicle.odometerReadingMiles ?? 0;
            Chassis = itemIn.vehicle.vin;
            Make = itemIn.vehicle.standard?.make;
            Model = itemIn.vehicle.standard?.model;
            Generation = itemIn.vehicle.generation;
            Derivative = itemIn.vehicle.derivative;
            DerivativeId = itemIn.vehicle.derivativeId;
            Trim = itemIn.vehicle.standard?.trim;
            BodyType = itemIn.vehicle.standard?.bodyType;
            FuelType = itemIn.vehicle.standard?.fuelType;
            TransmissionType = itemIn.vehicle.standard?.transmissionType;
            DriveTrain = itemIn.vehicle.standard?.drivetrain;
            Seats = itemIn.vehicle.seats;
            BadgeEngineSizeLitres = itemIn.vehicle.badgeEngineSizeLitres != null ? (decimal)itemIn.vehicle.badgeEngineSizeLitres : null;
            Doors = itemIn.vehicle.doors;
            EngineCapacityCC = itemIn.vehicle.engineCapacityCC;
            EnginePowerBHP = itemIn.vehicle.enginePowerBHP;
            Owners = itemIn.vehicle.owners;
            Colour = itemIn.vehicle.standard?.colour;
            SpecificColour = itemIn.vehicle.colour;
            Gears = itemIn.vehicle.gears;
            VehicleExciseDuty = itemIn.vehicle.vehicleExciseDutyWithoutSupplementGBP;
            Sector = itemIn.vehicle.sector;
            DateOnForecourt = itemIn.metadata?.dateOnForecourt;
            VehicleHasOptionsSpecified = itemIn.features?.Where(x => x.featureType == "Optional").Count() > 0;
            OdometerReadingMiles = itemIn.vehicle?.odometerReadingMiles;

            // Advert properties
            AttentionGrabber = itemIn.adverts?.retailAdverts?.attentionGrabber;
            Description = itemIn.adverts?.retailAdverts?.description;
            Description2 = itemIn.adverts?.retailAdverts?.description2;
            AutotraderAdvertStatus = itemIn.adverts?.retailAdverts?.autotraderAdvert?.status;
            AdvertiserAdvertStatus = itemIn.adverts?.retailAdverts?.advertiserAdvert?.status;
            LocatorAdvertStatus = itemIn.adverts?.retailAdverts?.locatorAdvert?.status;
            ExportAdvertStatus = itemIn.adverts?.retailAdverts?.exportAdvert?.status;
            ProfileAdvertStatus = itemIn.adverts?.retailAdverts?.profileAdvert?.status;
            WebsiteSearchIdentifier = itemIn.metadata.searchId;
            WebSiteStockIdentifier = itemIn.metadata.stockId;
            ImageURL = (itemIn.media.images.Count > 0) ? itemIn.media.images[0].href : null;
            ImageUrls = string.Join('|', itemIn.media.images.Select(x => x.href));
            VideoUrl = itemIn.media.video.href;
            TotalPrice = totalPrice;
            LifecycleStatus = itemIn.metadata.lifecycleState;
            OwnershipCondition = itemIn.vehicle?.ownershipCondition;
            ManufacturerApproved = itemIn.adverts?.manufacturerApproved;
            TwelveMonthsMot = itemIn.adverts?.twelveMonthsMot;
            SuppliedPrice = itemIn.adverts?.retailAdverts?.suppliedPrice?.amountGBP ?? 0;
            AdminFee = itemIn.adverts?.retailAdverts?.adminFee?.amountGBP;
            TotalPrice = itemIn.adverts?.retailAdverts?.totalPrice?.amountGBP ?? 0;
            ManufacturerRRP = itemIn.adverts?.retailAdverts?.manufacturerRRP?.amountGBP;
            VatStatus = itemIn.adverts?.retailAdverts?.vatStatus;
            PriceIndicatorRatingAtCurrentSelling = PriceStrategyClassifierService.ProvidePriceIndicatorName(itemIn.adverts?.retailAdverts?.priceIndicatorRating);
            DateOnForecourt = itemIn.metadata?.dateOnForecourt;
            AutotraderAdvertStatus = itemIn.adverts?.retailAdverts?.autotraderAdvert?.status;
            AdvertiserAdvertStatus = itemIn.adverts?.retailAdverts?.advertiserAdvert?.status;


            //Seller details
            Segment = itemIn.advertiser.segment ?? "Private";
            AdvertiserId = itemIn.advertiser?.advertiserId;
            CompetitorName = itemIn.advertiser.name;
            Website = itemIn.advertiser.website;
            Segment = itemIn.advertiser.segment ?? "Private";
            Town = itemIn.advertiser?.location?.town;
            County = itemIn.advertiser?.location?.county;
            Region = itemIn.advertiser?.location?.region;
            Postcode = itemIn.advertiser?.location?.postCode;
            if (itemIn.advertiser?.location?.latitude != null)
            {
               Latitude = (decimal)itemIn.advertiser?.location?.latitude.Value;
            }
            if (itemIn.advertiser?.location?.longitude != null)
            {
               Longitude = (decimal)itemIn.advertiser?.location?.longitude.Value;
            }

            //Features
            Features = itemIn.features?.Where(x => x.featureType == "Optional").Select(x => new AutotraderFeatureIncoming(x)).ToList();


         }
         catch (Exception ex)
         {
            { }
         }
      }


      //Getters
      public int? Year { get => FirstRegistered?.Year; }
      public decimal? PricePosition { get => ValuationAdjRetail > 0 ? TotalPrice / (decimal)ValuationAdjRetail : 0; }

      //Db items we create
      public int? AutotraderAdvertId { get; set; }
      //public AutotraderAdvert AutotraderAdvert { get; set; }
      //public AutotraderAdvertSnapshot AutotraderAdvertSnapshot { get; set; }
      //public AutotraderAdvertPriceChange AutotraderAdvertPriceChange { get; set; }
      //public List<AutotraderFeature> AutotraderFeatures { get; set; }

      //Features
      public List<AutotraderFeatureIncoming> Features { get; set; }


      //Valuations
      //Adjusted
      public int? ValuationAdjRetail { get; set; }
      public int? ValuationAdjTrade { get; set; }
      public int? ValuationAdjPartEx { get; set; }
      public int? ValuationAdjPrivate { get; set; }
      //Market Avg
      public int? ValuationMktAvRetail { get; set; }
      public int? ValuationMktAvTrade { get; set; }
      public int? ValuationMktAvPartEx { get; set; }
      public int? ValuationMktAvPrivate { get; set; }

      //Trended
      public int? ValuationMonthPlus1 { get; set; }
      public int? ValuationMonthPlus2 { get; set; }
      public int? ValuationMonthPlus3 { get; set; }

      // Vehicle properties
      public DateTime? FirstRegistered { get; set; }
      public string? VehicleType { get; set; }
      public string VehicleReg { get; set; }
      public int Odometer { get; set; }
      public string Chassis { get; set; }
      public string Make { get; set; }
      public string Model { get; set; }
      public string Generation { get; set; }
      public string Derivative { get; set; }
      public string DerivativeId { get; set; }
      public string Trim { get; set; }
      public string BodyType { get; set; }
      public string FuelType { get; set; }
      public string TransmissionType { get; set; }
      public string DriveTrain { get; set; }
      public int? Seats { get; set; }
      public decimal? BadgeEngineSizeLitres { get; set; }
      public int? Doors { get; set; }
      public int? EngineCapacityCC { get; set; }
      public int? EnginePowerBHP { get; set; }
      public int? Owners { get; set; }
      public string Colour { get; set; }
      public string SpecificColour { get; set; }
      public int? Gears { get; set; }
      public int? VehicleExciseDuty { get; set; }
      public string Sector { get; set; }
      public DateTime? DateOnForecourt { get; set; }
      public int? OdometerReadingMiles { get; set; }

      // Advert properties
      public string AttentionGrabber { get; set; }
      public string Description { get; set; }
      public string Description2 { get; set; }
      public string AutotraderAdvertStatus { get; set; }
      public string LocatorAdvertStatus { get; set; }
      public string ExportAdvertStatus { get; set; }
      public string ProfileAdvertStatus { get; set; }
      public string WebsiteSearchIdentifier { get; set; }
      public string WebSiteStockIdentifier { get; set; }
      public string ImageURL { get; set; }
      public string ImageUrls { get; set; }
      public string VideoUrl { get; set; }
      public int SuppliedPrice { get; set; }
      public int? AdminFee { get; set; }
      public int TotalPrice { get; set; }

      public int? ManufacturerRRP { get; set; }
      public string OwnershipCondition { get; set; }
      public string LifecycleStatus { get; set; }
      public bool VehicleHasOptionsSpecified { get; set; }
      public bool? ManufacturerApproved { get; set; }
      public bool? TwelveMonthsMot { get; set; }
      public string VatStatus { get; set; }
      public string PriceIndicatorRatingAtCurrentSelling { get; set; }

      //Seller details
      public string Segment { get; set; }
      public string Town { get; set; }
      public string County { get; set; }
      public string Region { get; set; }
      public string Postcode { get; set; }
      public decimal? Latitude { get; set; }
      public decimal? Longitude { get; set; }
      public string CompetitorName { get; set; }
      public string Website { get; set; }
      public string AdvertiserId { get; set; }
      public string AdvertiserAdvertStatus { get; set; }
   }



   public class GetPrivateAdDebug
   {
      public int PriceFrom { get; set; }
      public int PriceTo { get; set; }
      public int OdometerFrom { get; set; }
      public int OdometerTo { get; set; }
      public int Count { get; set; }
   }



}
