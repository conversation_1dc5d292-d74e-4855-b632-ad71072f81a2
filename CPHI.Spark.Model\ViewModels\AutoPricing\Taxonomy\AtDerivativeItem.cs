﻿using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing.Taxonomy
{
    public class AtDerivativeItem
    {
        public string derivativeId { get; set; }
        public string name { get; set; }
        public DateTime introduced { get; set; }
        public DateTime? discontinued { get; set; }
    }

    public class AtDerivativeItemFull: AtDerivativeItem
    {
        public string generationId { get; set; }
        public string vehicleType { get; set; }
        public string make { get; set; }
        public string model { get; set; }
        public string generation { get; set; }
        public string longName { get; set; }
        public string trim { get; set; }
        public string bodyType { get; set; }
        public string fuelType { get; set; }
        public string fuelDelivery { get; set; }
        public string transmissionType { get; set; }
        public string drivetrain { get; set; }
        public string cabType { get; set; }
        public string wheelbaseType { get; set; }
        public string roofHeightType { get; set; }
        public int seats { get; set; }
        public int doors { get; set; }
        public int valves { get; set; }
        public int gears { get; set; }
        public int cylinders { get; set; }
        public string cylinderArrangement { get; set; }
        public string engineMake { get; set; }
        public string valveGear { get; set; }
        public int axles { get; set; }
        public string countryOfOrigin { get; set; }
        public string driveType { get; set; }
        public bool startStop { get; set; }
        public int enginePowerPS { get; set; }
        public int engineTorqueNM { get; set; }
        public double? engineTorqueLBFT { get; set; }
        public int co2EmissionGPKM { get; set; }
        public int topSpeedMPH { get; set; }
        public double? zeroToSixtyMPHSeconds { get; set; }
        public double? zeroToOneHundredKMPHSeconds { get; set; }
        public decimal? badgeEngineSizeLitres { get; set; }
        public int engineCapacityCC { get; set; }
        public int enginePowerBHP { get; set; }
        public double? fuelCapacityLitres { get; set; }
        public string emissionClass { get; set; }
        public double? fuelEconomyNEDCExtraUrbanMPG { get; set; }
        public double? fuelEconomyNEDCUrbanMPG { get; set; }
        public decimal? fuelEconomyNEDCCombinedMPG { get; set; }
        public double? fuelEconomyWLTPLowMPG { get; set; }
        public double? fuelEconomyWLTPMediumMPG { get; set; }
        public double? fuelEconomyWLTPHighMPG { get; set; }
        public double? fuelEconomyWLTPExtraHighMPG { get; set; }
        public double? fuelEconomyWLTPCombinedMPG { get; set; }
        public int lengthMM { get; set; }
        public int heightMM { get; set; }
        public int widthMM { get; set; }
        public int? wheelbaseMM { get; set; }
        public double? bootSpaceSeatsUpLitres { get; set; }
        public double? bootSpaceSeatsDownLitres { get; set; }
        public string insuranceGroup { get; set; }
        public string insuranceSecurityCode { get; set; }
        public double? batteryRangeMiles { get; set; }
        public double? batteryCapacityKWH { get; set; }
        public double? batteryUsableCapacityKWH { get; set; }
        public int? minimumKerbWeightKG { get; set; }
        public int? grossVehicleWeightKG { get; set; }
        public double? grossCombinedWeightKG { get; set; }
        public double? grossTrainWeightKG { get; set; }
        public int boreMM { get; set; }
        public int strokeMM { get; set; }
        public double? payloadLengthMM { get; set; }
        public double? payloadWidthMM { get; set; }
        public double? payloadHeightMM { get; set; }
        public double? payloadWeightKG { get; set; }
        public double? payloadVolumeCubicMetres { get; set; }
        public bool? rde2Compliant { get; set; }
        public string sector { get; set; }
        public List<AtDerivativeChargeTime> chargeTimes { get; set; }
        public AtDerivativeOem oem { get; set; }
    }

    public class AtDerivativeChargeTime
    {
        public string chargerType { get; set; }
        public string chargerDescription { get; set; }
        public string chargerSupplyType { get; set; }
        public string connectorType { get; set; }
        public int startBatteryPercentage { get; set; }
        public int endBatteryPercentage { get; set; }
        public int durationMinutes { get; set; }
    }

    public class AtDerivativeOem
    {
        public string make { get; set; }
        public string model { get; set; }
        public string derivative { get; set; }
        public string bodyType { get; set; }
        public string transmissionType { get; set; }
        public string drivetrain { get; set; }
        public string wheelbaseType { get; set; }
        public string roofHeightType { get; set; }
        public string engineType { get; set; }
        public string engineTechnology { get; set; }
        public string engineMarketing { get; set; }
        public string editionDescription { get; set; }
        public string colour { get; set; }
    }
}
