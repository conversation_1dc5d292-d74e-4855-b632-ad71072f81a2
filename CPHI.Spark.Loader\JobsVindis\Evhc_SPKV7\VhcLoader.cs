﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
//using CPHI.Spark.WebScraper.Helpers;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{

    public class VhcJobVindis : IJob
    {

        public class NameAndSite
        {
            public string name;
            public int siteId;
        }

        private static readonly ILog Logger = LogManager.GetLogger(typeof(VhcJob));
        private const string fileSearch = "*Group EVHC Exclude Internal Daily Summary.xlsx";
        /*

| Column               | Example     | Sensitive |
|----------------------|-------------|-----------|
| Total Vehicles       | 188         | No        |
| EVHC Completed       | 185         | No        |
| No EVHC              | 3           | No        |
| % No EVHC            | 0.015957447 | No        |
| Value Reported       | 153925.47   | No        |
| Sold Value           | 27194.45    | No        |
| % Sold               | 0.17667284  | No        |
| Red Identified       | 87619.96    | No        |
| Avg Identified       | 473.6214054 | No        |
| Red Sold             | 22336.31    | No        |
| Avg Sold             | 120.7368108 | No        |
| % Red Sold           | 0.254922623 | No        |
| Red Deferred         | 6666.63     | No        |
| % Red Deferred       | 0.076085746 | No        |
| Red No Action        | 58617.02    | No        |
| % Red No Action      | 0.668991632 | No        |
| Total Red Items      | 168         | No        |
| Red Items Not Priced | 5           | No        |
| Not Priced %         | 0.029761905 | No        |
| Amber Identified     | 66305.51    | No        |
| Avg Identified       | 358.4081622 | No        |
| Amber Sold           | 4858.14     | No        |
| Avg Sold             | 26.26021622 | No        |
| % Amber Sold         | 0.073269024 | No        |
| Amber Deferred       | 40315.86    | No        |
| % Amber Deferred     | 0.608031821 | No        |
| Amber No Action      | 21131.51    | No        |
| % Amber No Action    | 0.318699155 | No        |
| Total Amber Items    | 169         | No        |

*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            string[] filePaths = Directory.GetFiles(ConfigService.incomingRoot, fileSearch );

            //check for presence of file, if so, return as already running
            if (LocksService.VindisVhc) { CentralLoggingService.ReportLock("VhcJobVindis"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.Vhc;
                if (age.Minutes > 120)
                {
                    PulsesService.Vhc = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern Group EVHC Exclude Internal Daily Summary.xlsx");
                }
                return;
            }


            //define Lists
            List<Person> dbPeople;



            //create a sites dictionary
            Dictionary<string, int> siteLookup = new Dictionary<string, int>()
                    {
                        {"Bedford Audi", 1 },
                        {"Bedford Volkswagen", 6},
                        {"Bury St Edmunds Skoda", 12},
                        {"Cambridge Audi", 2},
                        {"Fakenham Volkswagen", 8},
                        {"Huntingdon Audi", 3},
                        {"Huntingdon CV", 10},
                        {"Huntingdon Volkswagen", 9},
                        {"Milton Keynes Seat", 14},
                        {"Northampton Audi", 4},
                        {"Northampton CV", 11},
                        {"Northampton Commercial Vehicles", 11 },
                        {"Peterborough Audi", 5},
                        {"Skoda Service Centre Waterbeach", 13},
                        {"Skoda Service Centre Cambridge", 13},
                        {"Volkswagen Service Centre Waterbeach", 7},
                        {"Volkswagen Service Centre Cambridge", 7},
                        {"Ducati Cambridge", 15},
                        {"Bentley Cambridge", 16},
                        {"Three10 Automotive", 16},
                        {"Three 10 Automotive", 16},
                        {"Three 10 Automotive Bentley Cambridge", 16},
                        {"Northampton Commerical Vehicles", 11},
                        {"Huntingdon Commercial vehicles", 10},
                        {"Autonow", 20}
                    };

            LocksService.VindisVhc = true;
            if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
            {
                System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
            }
            DateTime start = DateTime.UtcNow;
            using (var db = new CPHIDbContext())

            {
                int errorCount = 0;
                LogMessage logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 3;

                try
                {

                    dbPeople = db.People.ToList();
                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found

                    //go through first file
                    string filePath = filePaths[0];

                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;
                    int changedCount = 0;

                    if (File.Exists(filePath.Replace(".xlsx", "-p.xlsx")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".xlsx", "-p.xlsx")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".xlsx", "-p.xlsx");

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<EVHCJob> incomingVhc = new List<EVHCJob>(1000);  //preset the list size (slightly quicker than growing it each time)
                    List<ServiceTransaction> incomingServiceTransactions = new List<ServiceTransaction>(1000);

                    HeadersAndRows rowsFromHelper = GetDataFromFilesService.GetExcelFileContents(newFilepath, 7, 8, null);

                    List<string> headers = rowsFromHelper.headers;

                    List<List<string>> rows = rowsFromHelper.rows;


                    for (int i = 0; i < headers.Count; i++)
                    {
                        headers[i] = headers[i].ToUpper();
                    }

                    int dealerIndex = headers.IndexOf("DEALER");
                    int jobDateIndex = headers.IndexOf("DATE");
                    int advisorIndex = headers.IndexOf("ADVISOR");
                    int totalVehiclesIndex = headers.IndexOf("TOTAL VEHICLES");
                    int evhcCompletedIndex = headers.IndexOf("EVHC COMPLETED");
                    int redIdentifiedIndex = headers.IndexOf("RED IDENTIFIED");
                    int redSoldIndex = headers.IndexOf("RED SOLD");
                    int amberIdentifiedIndex = headers.IndexOf("AMBER IDENTIFIED");
                    int amberSoldIndex = headers.IndexOf("AMBER SOLD");

                    List<NameAndSite> namesAndSites = new List<NameAndSite>();
                    List<Person> newPeople = new List<Person>();

                    string dealer = null;
                    DateTime finishedInterpetFile = DateTime.UtcNow;

                    //create any new people
                    foreach (var row in rows)
                    {
                        if (row[dealerIndex] != "") { dealer = row[dealerIndex]; }

                        if (dealer == "Total MTD") { continue; }

                        int siteId = siteLookup[dealer];
                        string name = row[advisorIndex];

                        if (name == "Total" || name == "") { continue; }

                        if (namesAndSites.Select(x => x.name).Contains(name)) continue;

                        NameAndSite nameAndSite = new NameAndSite();
                        nameAndSite.name = name;
                        nameAndSite.siteId = siteId;

                        if (!dbPeople.Select(x => x.Name).Contains(name)) namesAndSites.Add(nameAndSite);
                    }

                    foreach (var nameAndSite in namesAndSites)
                    {
                        Person person = new Person();

                        person.Id = 0;
                        person.Name = nameAndSite.name;
                        person.CurrentSite_Id = nameAndSite.siteId;
                        person.SellsUsed = false;
                        person.SellsNew = false;
                        person.LastUpdateDate = DateTime.UtcNow;
                        person.JobTitle = "Service Advisor";
                        person.JobRole = "Service Advisor";
                        person.IsRemoved = false;
                        person.IsUpdated = false;
                        person.HasLeft = false;
                        person.HasSales = false;
                        person.Sites = nameAndSite.siteId.ToString();
                        person.DoNotRemove = false;
                        person.AccessAllSites = false;
                        person.Email = "ADDED BY EVHC LOADER";
                        person.KnownAs = nameAndSite.name;
                        person.DealerGroup_Id = 3;
                        person.CurrentRetailerSite_Id = nameAndSite.siteId;

                        newPeople.Add(person);
                    }

                    //load new people
                    try
                    {
                        db.People.AddRange(newPeople);
                        db.SaveChanges();
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed adding new people range {err.ToString()}";
                        errorCount++;
                    }


                    dbPeople = db.People.ToList();

                    dealer = null;
                    DateTime jobDate = DateTime.Today;



                    //create the evhc items
                    foreach (var row in rows.Skip(0))
                    {
                        incomingCount++;

                        try
                        {

                            //set dealer name or skip if total

                            if (row[dealerIndex] != "") { dealer = row[dealerIndex]; }

                            if (dealer == "Total MTD") { continue; }

                            int siteId = siteLookup[dealer];

                            //if fails then must be a total row so skip

                            if (row[jobDateIndex] != "")
                            {
                                try { jobDate = DateTime.ParseExact(row[jobDateIndex], "dd/MM/yyyy HH:mm:ss", null); } catch { continue; }
                            }

                            //set advisor name or skip if total
                            string advisorName = row[advisorIndex];
                            if (advisorName == "Total" || advisorName == "") { continue; }

                            var advisor = dbPeople.FirstOrDefault(p => p.Name == advisorName);

                            if (advisor == null) { continue; }


                            int totalvehicles = int.Parse(row[totalVehiclesIndex]);
                            int evhcCompleted = int.Parse(row[evhcCompletedIndex]);

                            decimal redIdentified = decimal.Parse(row[redIdentifiedIndex]);
                            decimal redSold = decimal.Parse(row[redSoldIndex]);
                            decimal amberIdentified = decimal.Parse(row[amberIdentifiedIndex]);
                            decimal amberSold = decimal.Parse(row[amberSoldIndex]);

                            //major hack reporting for duty
                            for (int i = 0; i < evhcCompleted; i++)
                            {

                                EVHCJob e = new EVHCJob(); //initialise new one
                                e.Site_Id = siteId;
                                e.TechnicianId = null;
                                e.AdvisorId = advisor.Id;
                                e.JobDate = jobDate;
                                e.WIPNo = 0;
                                e.IsEVHCd = true;
                                e.RedWorkFound = redIdentified / evhcCompleted;
                                e.AmberWorkFound = amberIdentified / evhcCompleted;
                                e.RedWorkSold = redSold / evhcCompleted;
                                e.AmberWorkSold = amberSold / evhcCompleted;

                                incomingVhc.Add(e);
                            }


                            //major hack's second tour
                            for (int i = 0; i < totalvehicles; i++)
                            {
                                ServiceTransaction s = new ServiceTransaction
                                {
                                    MagicRef = 0,
                                    TransDate = jobDate,
                                    WIPNo = 0,
                                    WipLineNo = 0,
                                    Reg = null,
                                    Customer = null,
                                    AccountCode = null,
                                    Description = null,
                                    LabourType = null,
                                    HoursSold = 0,
                                    HoursTaken = 0,
                                    RetailSales = 0,
                                    GrossSales = 0,
                                    NetSales = 0,
                                    LabourCoS = 0,
                                    Channel_Id = null,
                                    Department_Id = null,
                                    Franchise_Id = null,
                                    RTS_Id = null,
                                    ServiceAdvisor_Id = advisor.Id,
                                    Site_Id = siteId,
                                    Technician_Id = null
                                }; 

                                incomingServiceTransactions.Add(s);
                            }


                            Console.WriteLine(incomingCount);
                        }

                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, VHC: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                            errorCount++;
                            continue;
                        }

                    }


                    var newItemsEvhc = incomingVhc;
                    newCount = newItemsEvhc.Count;

                    if (newCount > 0)
                    {

                        var newItemsServiceTransactions = incomingServiceTransactions;

                        //find earliest incoming
                        DateTime earliestIncomingEvhc = newItemsEvhc.OrderBy(x => x.JobDate).First().JobDate;

                        //find items to remove
                        List<EVHCJob> toRemoveEvhc = db.EVHCJobs.Where(x => x.JobDate >= earliestIncomingEvhc).ToList();
                        List<ServiceTransaction> toRemoveServiceTransaction = db.ServiceTransactions.Where(x => x.TransDate >= earliestIncomingEvhc).ToList();
                        removedCount = toRemoveEvhc.Count;

                        try
                        {
                            db.EVHCJobs.RemoveRange(toRemoveEvhc);
                            db.EVHCJobs.AddRange(newItemsEvhc);  //add them all in one go
                            db.ServiceTransactions.RemoveRange(toRemoveServiceTransaction);
                            db.ServiceTransactions.AddRange(newItemsServiceTransactions);  //add them all in one go

                            db.SaveChanges();
                            //run the populate thing
                            using (var dapper = new Dapperr())
                            {
                                await dapper.ExecuteAsync("POPULATE_ExternalWipsVindis", null, System.Data.CommandType.StoredProcedure);
                            }

                        }

                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                            errorCount++;
                        }
                        DateTime finishedUpdateDb = DateTime.UtcNow;

                        logMessage.FinishDate = DateTime.UtcNow;
                        logMessage.ProcessedCount = incomingVhc.Count;
                        logMessage.AddedCount = newCount;
                        logMessage.RemovedCount = removedCount;
                        logMessage.ChangedCount = changedCount;
                        logMessage.IsCompleted = true;
                        logMessage.ErrorCount = errorCount;


                        Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingVhc.Count} item(s) interpreted, {newCount} new, {removedCount} removed.");
                        try
                        {
                            File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));
                            if (errorCount > 0)
                            {
                                //we have errors so use the reporter
                                logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                                
                                await CentralLoggingService.ReportError("Vhc", logMessage, true);
                            }
                            else
                            {
                                //no errors so just save the log
                                logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                                logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                                logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                                db.LogMessages.Add(logMessage);
                                db.SaveChanges();
                                
                            }

                            GlobalParam dealLatestsUpdateDate = db.GlobalParams.First(x => x.Description == "evhcUpdateDate");
                            dealLatestsUpdateDate.DateFrom = DateTime.UtcNow; // 
                            dealLatestsUpdateDate.TextValue = DateTime.UtcNow.ToLongDateString();
                            db.SaveChanges();



                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                            errorCount++;

                            await CentralLoggingService.ReportError("Vhc", logMessage, true);
                        }

                        //trigger cache rebuild
                        await UpdateWebAppService.Trigger("EVHCJobs");
                        await UpdateWebAppService.Trigger("ServiceTransactions");
                        stopwatch.Stop();

                    }
                    // No new items to add
                    else
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));
                        logMessage.FinishDate = DateTime.UtcNow;
                        logMessage.ProcessedCount = incomingVhc.Count;
                        logMessage.RemovedCount = 0;
                        logMessage.AddedCount = 0;
                        Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingVhc.Count} item(s) interpreted, no new items.");
                        db.LogMessages.Add(logMessage);
                        db.SaveChanges();
                        
                        stopwatch.Stop();
                    }


                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();
                    await EmailerService.SendErrorMail("Vindis evhc loader failed", err.StackTrace);
                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;

                    await CentralLoggingService.ReportError("Vhc", logMessage);
                }

                finally
                {
                    db.ChangeTracker.Clear();

                    LocksService.VindisVhc = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "Vindis",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }



            }
        }



    }


}
