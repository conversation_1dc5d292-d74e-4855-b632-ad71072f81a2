﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Dapper;
using log4net;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using MoreLinq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;


namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IAutotraderAdvertsDataAccess
   {
      Task<List<AutotraderAdvertLoadResult>> CarryOutSQLMerge();
      Task<List<AutotraderAdIdAndHash>> GetAutotraderAdIdAndHashes(List<string> websiteStockIdentifiers);
      Task<List<AverageVehicleAdvert>> GetAverageDaysInStock(DealerGroupName dealerGroup);
      Task<IEnumerable<string>> GetExistingAdvertChassisList();
      Task LoadToImportTable(List<AutotraderAdvertLoad> adverts);
      Task RemoveAdvertsNotSeen(List<string> websiteStockIdentifiers, bool isPrivate);
      Task<List<AutotraderAdvert>> SaveIncomingNewAdverts(List<AutotraderAdvertLoad> advertLoads, ILog logger);

      //Task<List<AutotraderAdvert>> SaveIncomingNewAdverts(List<AutotraderAdvertLoad> advertLoads);
      Task TruncateLoadTable();
      Task UpdateLatestAutotraderSnapForEachAd(bool isPrivate);
      Task<Dictionary<string, int>> UpsertAutotraderFeatures(List<AutotraderFeatureIncoming> incomingFeatures);
      Task UpsertAutotraderSnapshots(List<AutotraderAdvertSnapshotIncoming> incomingSnaps);
   }

   public class AutotraderAdvertsDataAccess : IAutotraderAdvertsDataAccess
   {
      private readonly string _connectionString;
      public AutotraderAdvertsDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }




      public async Task<IEnumerable<string>> GetExistingAdvertChassisList()
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            DateTime twoDaysAgo = DateTime.Now.Date.AddDays(-2);
            var toReturn = await db.AutotraderAdverts.Where(x => x.RemovedDate == null || x.RemovedDate >= twoDaysAgo).Select(x => x.Chassis).Distinct().ToListAsync();
            return toReturn;
         }

      }

      public async Task<Dictionary<string, int>> UpsertAutotraderFeatures(List<AutotraderFeatureIncoming> incomingFeatures)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            // Build a lookup of existing UniqueIds to their PK Ids
            var existingItems = await db.AutotraderFeatures
           .Select(f => new
           {
              Id = f.Id,
              UniqueId =
                   (f.Name ?? "Unknown") +
                   (f.FeatureType ?? "Unknown") +
                   (f.Category ?? "Unknown") +
                   (f.RarityRating ?? "Unknown") +
                   (f.ValueRating ?? "Unknown")
           })
           .ToListAsync();


            var uniqueIdToIdMap = existingItems
                .ToDictionary(x => x.UniqueId, x => x.Id);

            // Determine which incoming items are new
            var newFeatures = incomingFeatures
                .Where(x => !uniqueIdToIdMap.ContainsKey(x.UniqueId))
                .Select(x => new AutotraderFeature
                {
                   Name = x.Name,
                   FeatureType = x.FeatureType,
                   Category = x.Category,
                   RarityRating = x.RarityRating,
                   ValueRating = x.ValueRating
                })
                .ToList();


            // Add and save new items
            if (newFeatures.Any())
            {
               await db.AutotraderFeatures.AddRangeAsync(newFeatures);
               await db.SaveChangesAsync();

               // Add new entries to the map
               foreach (var f in newFeatures)
               {
                  var uniqueId = (f.Name ?? "Unknown") +
                   (f.FeatureType ?? "Unknown") +
                   (f.Category ?? "Unknown") +
                   (f.RarityRating ?? "Unknown") +
                   (f.ValueRating ?? "Unknown");
                  uniqueIdToIdMap[uniqueId] = f.Id;
               }
            }

            return uniqueIdToIdMap;
         }
      }
      public async Task UpsertAutotraderSnapshots(List<AutotraderAdvertSnapshotIncoming> incomingSnaps)
      {
         //2.1 Create any snapshots for those newly created adverts


         //3. CRUD snapshots for all incoming adverts.  CRUD logic is:
         // if we have an existing snapshot at any point in the past including today or prior

         //       Compare prices.        IF they are different, we create an AdvertPriceChange
         //                            ELSE not different, we do not create a price change
         // If the existing snapshot is from today, we now will update it's properties to match those incoming
         // ELSE it's from before today, we must now create today's snapshot

         //save these new and update snapshots, and new price changes

         using (var db = new CPHIDbContext(_connectionString))
         {
            var incomingAdIds = incomingSnaps.Select(x => x.AutotraderAdvert_Id).Distinct().ToList();
            //find most recent snapshot from db for each incoming Advert, using logic of highest Id is most recent
            var mostRecentSnapshots = await db.AutotraderAdvertSnapshots
                .Where(x => incomingAdIds.Contains(x.AutotraderAdvert_Id))
                .GroupBy(x => x.AutotraderAdvert_Id)
                .Select(g => g.OrderByDescending(x => x.Id).First())
                .ToListAsync();

            var recentsDict = mostRecentSnapshots.ToDictionary(x => x.AutotraderAdvert_Id, x => x);


            List<AutotraderAdvertPriceChange> priceChangesToSave = new List<AutotraderAdvertPriceChange>();
            List<AutotraderAdvertSnapshot> snapshotsToSave = new List<AutotraderAdvertSnapshot>();
            foreach (var incomingSnap in incomingSnaps)
            {
               if (recentsDict.TryGetValue(incomingSnap.AutotraderAdvert_Id, out var recentSnap))
               {
                  //create price change if required
                  if (recentSnap.TotalPrice != incomingSnap.TotalPrice)
                  {
                     priceChangesToSave.Add(new AutotraderAdvertPriceChange()
                     {
                        AutotraderAdvert_Id = incomingSnap.AutotraderAdvert_Id,
                        PriceIndicatorWas = recentSnap.PriceIndicatorRatingAtCurrentSelling,
                        PriceIndicatorNow = incomingSnap.PriceIndicatorRatingAtCurrentSelling,
                        SuppliedPriceWas = recentSnap.SuppliedPrice ?? 0,
                        SuppliedPriceNow = incomingSnap.SuppliedPrice ?? 0,
                        TotalPriceWas = recentSnap.TotalPrice,
                        TotalPriceNow = incomingSnap.TotalPrice,
                        AdminFeeWas = recentSnap.AdminFee ?? 0,
                        AdminFeeNow = incomingSnap.AdminFee ?? 0,
                        CreatedDate = DateTime.Now
                     });
                  }

                  if (recentSnap.CreatedDate.Date < incomingSnap.CreatedDate.Date)
                  {
                     //recent snap is an earlier day, so create one for today
                     AutotraderAdvertSnapshot snapToSave = new AutotraderAdvertSnapshot(incomingSnap);
                     snapshotsToSave.Add(snapToSave);
                  }
                  else
                  {
                     //recent snap is current day, so update it
                     recentSnap.UpdatedDate = incomingSnap.UpdatedDate;

                     // Valuations - Adjusted
                     recentSnap.ValuationAdjRetail = incomingSnap.ValuationAdjRetail;
                     recentSnap.ValuationAdjTrade = incomingSnap.ValuationAdjTrade;
                     recentSnap.ValuationAdjPartEx = incomingSnap.ValuationAdjPartEx;
                     recentSnap.ValuationAdjPrivate = incomingSnap.ValuationAdjPrivate;

                     // Valuations - Market Average
                     recentSnap.ValuationMktAvRetail = incomingSnap.ValuationMktAvRetail;
                     recentSnap.ValuationMktAvTrade = incomingSnap.ValuationMktAvTrade;
                     recentSnap.ValuationMktAvPartEx = incomingSnap.ValuationMktAvPartEx;
                     recentSnap.ValuationMktAvPrivate = incomingSnap.ValuationMktAvPrivate;

                     // Trended Valuations
                     recentSnap.ValuationMonthPlus1 = incomingSnap.ValuationMonthPlus1;
                     recentSnap.ValuationMonthPlus2 = incomingSnap.ValuationMonthPlus2;
                     recentSnap.ValuationMonthPlus3 = incomingSnap.ValuationMonthPlus3;

                     // Price properties
                     recentSnap.SuppliedPrice = incomingSnap.SuppliedPrice;
                     recentSnap.AdminFee = incomingSnap.AdminFee;
                     recentSnap.TotalPrice = incomingSnap.TotalPrice;
                     recentSnap.PriceIndicatorRatingAtCurrentSelling = incomingSnap.PriceIndicatorRatingAtCurrentSelling;

                  }
               }
               else
               {
                  //no existing snapshot, create one
                  snapshotsToSave.Add(new AutotraderAdvertSnapshot(incomingSnap));
               }
            }

            await db.AutotraderAdvertSnapshots.AddRangeAsync(snapshotsToSave);
            await db.AutotraderAdvertPriceChanges.AddRangeAsync(priceChangesToSave);

            await db.SaveChangesAsync();
         }
      }

      public async Task<Dictionary<string, int>> UpsertAutotraderAdvertFeatureMappings(List<AutotraderAdvertFeatureIncoming> incomingMappings)
      {
         //dedupe, technically possible for an advert to be duplicated if it was price changed at exactly the wrong time in our search
         incomingMappings = incomingMappings
            .GroupBy(x => x.UniqueId)
            .Select(g => g.First())
            .ToList();


         using (var db = new CPHIDbContext(_connectionString))
         {
            List<int> incomingAdIds = incomingMappings.Select(x => x.AutotraderAdvert_Id).Distinct().ToList();

            var existingMappings = await db.AutotraderAdvertFeatures.Where(x => incomingAdIds.Contains(x.AutotraderAdvert_Id)).ToListAsync();
            var uniqueIdToIdMap = existingMappings.ToDictionary(x => x.AutotraderFeature_Id.ToString() + '|' + x.AutotraderAdvert_Id.ToString(), x => x.Id, StringComparer.OrdinalIgnoreCase);

            //------------------------
            //New Items
            //------------------------
            // Determine which incoming items are new
            var newItems = incomingMappings
                .Where(x => !uniqueIdToIdMap.ContainsKey(x.UniqueId))
                .Select(x => new AutotraderAdvertFeature
                {
                   AutotraderAdvert_Id = x.AutotraderAdvert_Id,
                   AutotraderFeature_Id = x.AutotraderFeature_Id,
                })
                .ToList();

            // Add and save new items
            if (newItems.Any())
            {

               await db.AutotraderAdvertFeatures.AddRangeAsync(newItems);
               await db.SaveChangesAsync();

               // Add new entries to the map
               foreach (var newItem in newItems)
               {
                  var uniqueId = newItem.AutotraderFeature_Id.ToString() + '|' + newItem.AutotraderAdvert_Id.ToString();
                  uniqueIdToIdMap[uniqueId] = newItem.Id;
               }
            }

            //------------------------
            //Remove Items
            //------------------------
            //Now remove items for the distinct list of advertIds we have
            List<AutotraderAdvertFeature> toRemove = new();
            var existingMappingsByAdvert = existingMappings.ToLookup(x => x.AutotraderAdvert_Id);
            foreach (var advertGroup in incomingMappings.ToLookup(x => x.AutotraderAdvert_Id))
            {
               var existingMappingsThisAd = existingMappingsByAdvert[advertGroup.Key];
               var incomingMappingsThisAd = advertGroup.Select(x => x.AutotraderFeature_Id).ToList();
               foreach (var existingMapping in existingMappingsThisAd)
               {
                  if (!incomingMappingsThisAd.Contains(existingMapping.AutotraderFeature_Id))
                  {
                     toRemove.Add(existingMapping);
                  }
               }
            }
            db.AutotraderAdvertFeatures.RemoveRange(toRemove);
            return uniqueIdToIdMap;
         }
      }

      public async Task RemoveAdvertsNotSeen(List<string> websiteStockIdentifiers, bool isPrivate)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            var existing = await db.AutotraderAdverts
               .Where(x=>x.IsPrivateAd == isPrivate)
               .Where(x=>x.IsRemoved == false)
               .Select(x=>new AutotraderAdIdAndHash(x.WebSiteStockIdentifier, x.ContentHash,x.Id))
               .ToListAsync();
            
            var incoming = websiteStockIdentifiers.ToHashSet();

            var removeIds = existing.Where(x => !incoming.Contains(x.WebsiteStockIdentifier)).Select(x=>x.AdId).ToList();


               await db.AutotraderAdverts
                   .Where(x =>removeIds.Contains(x.Id))
                   .ExecuteUpdateAsync(x => x
                       .SetProperty(a => a.IsRemoved, true)
                       .SetProperty(a => a.RemovedDate, DateTime.Now));
         }
      }



      public async Task<List<AutotraderAdIdAndHash>> GetAutotraderAdIdAndHashes(List<string> websiteStockIdentifiers)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var toReturn = await db.AutotraderAdverts
                                          .Where(x => websiteStockIdentifiers.Contains(x.WebSiteStockIdentifier))
                                          .Where(x=>x.IsRemoved == false)
                                          .Select(x => new AutotraderAdIdAndHash(x.WebSiteStockIdentifier, x.ContentHash, x.Id))
                                          .ToListAsync();
            return toReturn;
         }
      }

      public async Task<List<AutotraderAdvert>> SaveIncomingNewAdverts(List<AutotraderAdvertLoad> advertLoads, ILog logger)
      {

         List<AutotraderAdvert> newAdverts = new();
         for (int i = 0; i < advertLoads.Count; i++)
         {
            try
            {
               using var db = new CPHIDbContext(_connectionString);
               var toSave = new AutotraderAdvert(advertLoads[i]);
               db.AutotraderAdverts.Add(toSave);
               await db.SaveChangesAsync();
               newAdverts.Add(toSave);
            }
            catch (Exception ex)
            {
               logger.Error($"❌ Failed on record {i} — VehicleReg: {advertLoads[i].VehicleReg}, Id: {advertLoads[i].Id}");
               logger.Error(ex.Message);

               var bad = newAdverts[i];

               foreach (var prop in bad.GetType().GetProperties())
               {
                  var val = prop.GetValue(bad);
                  var maxLengthAttr = prop.GetCustomAttribute<MaxLengthAttribute>();
                  var columnAttr = prop.GetCustomAttribute<ColumnAttribute>();

                  if (val is string s && maxLengthAttr != null)
                  {
                     logger.Error($"{prop.Name}: {s.Length}/{maxLengthAttr.Length}");
                  }
                  else if (val is string s2)
                  {
                     logger.Error($"{prop.Name}: {s2.Length} (no MaxLength)");
                  }
                  else if (val is decimal d && columnAttr?.TypeName?.StartsWith("decimal") == true)
                  {
                     logger.Error($"{prop.Name}: {d} (decimal) → raw value");
                  }


               }
               break;
            }
         }


         return newAdverts;



         //using (var db = new CPHIDbContext(_connectionString))
         //{
         //   var newAdverts = advertLoads.Select(x => new AutotraderAdvert(x)).ToList();

            


         //   await db.AutotraderAdverts.AddRangeAsync(newAdverts);
         //   await db.SaveChangesAsync();
         //   return newAdverts;
         //}
      }

      public async Task TruncateLoadTable()
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string query = $"TRUNCATE TABLE [import].[AutotraderAdvertLoads]";
            await dapper.ExecuteAsync(query, null, System.Data.CommandType.Text);
         }
      }

      public async Task LoadToImportTable(List<AutotraderAdvertLoad> adverts)
      {
         var dataTable = adverts.ToDataTable();
         using (SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(_connectionString))
         {
            foreach (DataColumn col in dataTable.Columns)
            {
               sqlBulkCopy.ColumnMappings.Add(col.ColumnName, col.ColumnName);
            }
            sqlBulkCopy.DestinationTableName = $"[import].[AutotraderAdvertLoads]";
            await sqlBulkCopy.WriteToServerAsync(dataTable);
         }
      }

      public async Task<List<AutotraderAdvertLoadResult>> CarryOutSQLMerge()
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            //var spParams = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
            var results = await dapper.GetAllAsync<AutotraderAdvertLoadResult>("[import].[MERGE_AutotraderAdverts]", null, commandType: System.Data.CommandType.StoredProcedure,null,3000);
            return results.ToList();
         }
      }

      public async Task<List<AverageVehicleAdvert>> GetAverageDaysInStock(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var spParams = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
            var results = await dapper.GetAllAsync<AverageVehicleAdvert>("[autoprice].[GET_AverageVehicleAdverts]", spParams, commandType: System.Data.CommandType.StoredProcedure, null, 3000);
            return results.ToList();
         }
      }

      public async Task UpdateLatestAutotraderSnapForEachAd(bool isPrivate)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var spParams = new DynamicParameters(new { isPrivate = isPrivate });
            await dapper.GetAllAsync<AutotraderAdvertLoadResult>("[autoprice].[UPDATE_LatestAutotraderSnapForEachAd]", spParams, commandType: System.Data.CommandType.StoredProcedure,null, 3000);
         }
      }






   }
}
