using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;

namespace CPHI.WebScraper.ViewModel
{
   public class SalesmasterVehicle
   {
      private List<RetailerSite> retailers;

      public string Reg { get; set; }
      public int Price { get; set; }
      public int RetailerSiteId { get; set; }
      public bool PriceChanged { get; set; } = false;

      // Properties for SaveChangeResult
      public int PriceChangeId { get; set; }
      public bool IsAutoPriceChange { get; set; }
      public bool SavedOk { get; set; }
      public string SaveError { get; set; }
      public string WebsiteStockIdentifier { get; set; } //Used by Three10 to update AT
      public int RetailerId { get; set; } //Used by Three10 to update AT

      public SalesmasterVehicle(PricingChangeMinimal pr, List<RetailerSite> retailers)
      {
         Reg = pr.VehicleReg;
         Price = pr.NewPrice;
         RetailerSiteId = pr.RetailerSiteId;
         PriceChangeId = pr.PriceChangeId;
         IsAutoPriceChange = true;
         WebsiteStockIdentifier = pr.WebsiteStockIdentifier;
         RetailerId = retailers.Find(x => x.Id == pr.RetailerSiteId)?.RetailerId ?? 0;
      }

      // Constructor for test items
      public SalesmasterVehicle(string reg, int price, int retailerSiteId)
      {
         Reg = reg;
         Price = price;
         RetailerSiteId = retailerSiteId;
      }

     
   }
}
