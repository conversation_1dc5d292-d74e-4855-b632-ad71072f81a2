﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using System.Diagnostics;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Reflection;

namespace CPHI.Spark.Loader
{
    public class ActivitiesJobVindis : IJob
    {


        private static readonly ILog Logger = LogManager.GetLogger(typeof(ActivitiesJobVindis));
        private string fileSearch = "*SPKV8.xlsx";
        /*
| Column                       | Example                          | Sensitive               |
|------------------------------|----------------------------------|-------------------------|
| Retailer                     | Vindis VW Cambridge              | No                      |
| New/Used                     | Used                             | No                      |
| Make                         | Volkswagen                       | No                      |
| Model                        | GOLF HATCHBACK                   | No                      |
| Customer Name                | Mr <PERSON>                   | YES - customer name     |
| Customer Email               | <EMAIL>        | YES - customer email    |
| Customer Postcode            | TBC                              | YES - customer postcode |
| Customer Home Phone Number   |                                  | YES - customer phone    |
| Customer Mobile Number 1     | 07943969830                      | YES - customer phone    |
| Customer Mobile Number 2     |                                  | No                      |
| Enquiry Created              | 01/04/2023 07:39                 | No                      |
| Created By                   | Thomas Brinkman                  | No                      |
| Method of Contact            | Internet/Email                   | No                      |
| Source of Enquiry            | heycar.co.uk                     | No                      |
| Lead Source                  | Rapid Response                   | No                      |
| Lead Type                    | General Enquiry                  | No                      |
| Lead Allocated To            | Thomas Brinkman                  | No                      |
| Customer Transferred         |                                  | No                      |
| Transferred From             |                                  | No                      |
| Transferred To               |                                  | No                      |
| Last Customer Transferred    |                                  | No                      |
| Last Transferred To          |                                  | No                      |
| Appointment Created          |                                  | No                      |
| Appointment Kept             |                                  | No                      |
| Initial Showroom Appointment |                                  | No                      |
| Test Drive Registered        |                                  | No                      |
| Next Contact Date            |                                  | No                      |
| Appointment Event            |                                  | No                      |
| Offer Made                   |                                  | No                      |
| Order                        |                                  | No                      |
| Enquiry Status               | Lost Sale                        | No                      |
| Lost Sale Accepted           | 05/04/2023 09:43                 | No                      |
| Lost Sale Reason             | Customer not responding to calls | No                      |
| Delivery Date                |                                  | No                      |
| Lost Sale Created By         | Thomas Brinkman                  | No                      |
| Stock Number                 |                                  | No                      |
| Registration Number          | ML70FUE                          | No                      |
| Vehicle Base Price           |                                  | No                      |
| Salesperson                  | Thomas Brinkman                  | No                      |
| Range                        | GOLF                             | No                      |
| Pre-Order Status             |                                  | No                      |
| Pre-Order Amount Paid        | 0.00                             | No                      |
| Closed New HO                |                                  | No                      |
| Closed Used HO               |                                  | No                      |
| Lost Sale Created By         | Thomas Brinkman                  | No                      |
| Lost Opp Reason              | Customer not responding to calls | No                      |
| Registration Number          | ML70FUE                          | No                      |
| Original Lead Site           | Vindis VW Cambridge              | No                      |
| Enquiry Number               | 18186615                         | No                      |

*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            string[] filePaths = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot,fileSearch ));

            //check for presence of file, if so, return as already running
            if (LocksService.VindisActivities) { CentralLoggingService.ReportLock("ActivitiesJobVindis"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.Activities;
                if (age.Minutes > 120)
                {
                    PulsesService.Activities = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPKV8.xlsx");
                }
                return;
            }


            //define Lists
            List<Site> dbSites;
            LocksService.VindisActivities = true;
            if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
            {
                System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
            }
            DateTime start = DateTime.UtcNow;

            using (var db = new CPHIDbContext())

            {

                int errorCount = 0;
                LogMessage logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 3;

                try
                {


                    dbSites = db.Sites.ToList();


                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found


                    //go through first file
                    string filePath = filePaths[0];


                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;
                    int changedCount = 0;
                    //bool sales;

                    if (File.Exists(filePath.Replace(".xlsx", "-p.xlsx")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".xlsx", "-p.xlsx")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".xlsx", "-p.xlsx");

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<SarsLine> incomingActivities = new List<SarsLine>(1000);  //preset the list size (slightly quicker than growing it each time)

                    HeadersAndRows rowsFromHelper = GetDataFromFilesService.GetExcelFileContents(newFilepath, 0, 1, null);

                    List<string> headers = rowsFromHelper.headers;

                    List<List<string>> rows = rowsFromHelper.rows;

                    int retailerIndex = headers.IndexOf("Retailer");
                    int enquiryCreatedIndex = headers.IndexOf("Enquiry Created");

                    foreach (var row in rows)
                    {
                        incomingCount++;


                        try
                        {

                            if (row.Count != headers.Count)
                            {
                                //something weird happened, not got enough cells for the number of headerCols, skip this record
                                logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {row.Count} cells and needed {headers.Count} file is {filePath}";
                                errorCount++;
                                continue;
                            }

                            //lookup objects required

                            //site
                            string siteName = row[retailerIndex];
                            DateTime activityDate = DateTime.ParseExact(row[enquiryCreatedIndex], "dd/MM/yyyy HH:mm", null);
                            int siteId = 0;

                            //branch & department
                            switch (siteName)
                            {
                                case "Vindis Audi Northampton": { siteId = 4; break; }
                                case "Vindis VW Bedford": { siteId = 6; break; }
                                case "Vindis VW Cambridge": { siteId = 7; break; }
                                case "Vindis Audi Peterborough": { siteId = 5; break; }
                                case "Vindis Bentley Cambridge": { siteId = 16; break; }
                                case "Three10 Automotive": { siteId = 16; break; }
                                case "Three 10 Automotive": { siteId = 16; break; }
	                            case "Three 10 Automotive Bentley Cambridge": { siteId = 16; break; }
                                case "Vindis Ducati Cambridge ": { siteId = 15; break; }
                                case "Vindis Van Centre Northampton": { siteId = 11; break; }
                                case "Vindis VW Huntingdon": { siteId = 9; break; }
                                case "Vindis Audi Cambridge": { siteId = 2; break; }
                                case "Vindis Audi Huntingdon": { siteId = 3; break; }
                                case "Vindis Audi Bedford": { siteId = 1; break; }
                                case "Cookes Of Fakenham": { siteId = 8; break; }
                                case "Vindis Skoda Cambridge": { siteId = 13; break; }
                                case "Vindis Skoda Bury St. Edmunds": { siteId = 12; break; }
                                case "Vindis SEAT Milton Keynes": { siteId = 14; break; }
                                case "Vindis AutoNow Cambridge": { siteId = 18; break; }
                                case "Vindis AutoNow Bury St. Edmunds": { siteId = 17; break; }
                                case "Vindis AutoNow Fakenham": { siteId = 19; break; }
                                case "Vindis AutoNow Sawston": { siteId = 20; break; }
                                default: { continue; }
                            }

                            if (siteId == 0) { continue; } //if no matching siteId found then skip this row

                            SarsLine e = new SarsLine(); //initialise new one

                            e.WalkInNew = 1;
                            e.Day = activityDate;
                            e.Site_Id = siteId;
                            e.SalesExec_Id = 1;

                            incomingActivities.Add(e);

                            Console.WriteLine(incomingCount);

                        }

                        catch (Exception err)
                        {
                            if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Activity: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                            errorCount++;
                            continue;
                        }
                    }

                    List<SarsLine> newItems = incomingActivities;
                    DateTime finishedInterpetFile = DateTime.UtcNow;
                    newCount = newItems.Count;

                    //find earliest incoming
                    DateTime earliestIncomingActivity = newItems.OrderBy(x => x.Day).First().Day;

                    //find items to remove
                    List<SarsLine> toRemove = db.SarsLines.Where(x => x.Day >= earliestIncomingActivity).ToList();
                    removedCount = toRemove.Count;

                    try
                    {
                        db.SarsLines.RemoveRange(toRemove);
                        db.SarsLines.AddRange(newItems);  //add them all in one go
                        db.SaveChanges();
                    }


                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                        errorCount++;
                    }
                    DateTime finishedUpdateDb = DateTime.UtcNow;

                    logMessage.FinishDate = DateTime.UtcNow;
                    logMessage.ProcessedCount = incomingActivities.Count;
                    logMessage.AddedCount = newCount;
                    logMessage.RemovedCount = removedCount;
                    logMessage.ChangedCount = changedCount;
                    logMessage.IsCompleted = true;
                    logMessage.ErrorCount = errorCount;



                    Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingActivities.Count} item(s) interpreted, added {newCount} new, changed {changedCount}");
                    try
                    {
                        RemoveUnwantedData(headers, newFilepath);
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));
                        if (errorCount > 0)
                        {
                            //we have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            await CentralLoggingService.ReportError("ActivitiesLoader", logMessage, true);
                            
                        }
                        else
                        {
                            //no errors so just save the log
                            logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                            logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                            logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            
                        }
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;
                        
                        await CentralLoggingService.ReportError("ActivitiesLoader", logMessage, true);
                    }

                    //trigger cache rebuild
                    await UpdateWebAppService.Trigger("SarsLines");
                    stopwatch.Stop();
                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();
                    await EmailerService.SendErrorMail("Vindis activities loader failed", err.StackTrace);
                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                    
                    await CentralLoggingService.ReportError("ActivitiesLoader", logMessage);

                }

                finally
                {
                    db.ChangeTracker.Clear();

                    LocksService.VindisActivities = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "Vindis",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }



            }

        }

        private void RemoveUnwantedData(List<string> headers, string filepath)
        {
            int toDelete1 = headers.IndexOf("Customer Name") + 1;
            int toDelete2 = headers.IndexOf("Customer Email") + 1;
            int toDelete3 = headers.IndexOf("Customer Postcode") + 1;
            int toDelete4 = headers.IndexOf("Customer Home Phone Number") + 1;
            int toDelete5 = headers.IndexOf("Customer Mobile Number 2") + 1;
            int toDelete6 = headers.IndexOf("Customer Mobile Number 1") + 1;

            int[] columnIndicesToDelete = { toDelete1, toDelete2, toDelete3, toDelete4, toDelete5, toDelete6 };

            HelpersService.DeleteColumnsAndSaveXLSX(filepath, filepath, columnIndicesToDelete, 1, false);

        }



    }


}
