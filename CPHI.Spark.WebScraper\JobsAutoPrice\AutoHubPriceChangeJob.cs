using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.WebScraper.Services;
using OpenQA.Selenium.Support.UI;
using System.Threading;
using CPHI.WebScraper.ViewModel;
using System.Collections.Generic;
using System.Linq;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.Repository;
using SeleniumExtras.WaitHelpers;
using CPHI.Repository;

namespace CPHI.Spark.WebScraper.Jobs
{
    [DisallowConcurrentExecution]
    public class AutoHubPriceChangeJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(AutoHubPriceChangeJob));
        private static IWebDriver _driver;
        private const string homePageLink = "https://app.autohub.uk/sign-in"; 
        private DealerGroupName dealerGroup;

        public void Execute() { }

        public async Task Execute(IJobExecutionContext context)
        {
            logger.Info("");
            logger.Info("========================= Starting AutoHubPriceChangeJob =====================================");

            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            dealerGroup = DealerGroupName.LMCOfFarnham; // Update this based on your requirements

            try
            {
                logger.Info("Starting AutoHub price change job...");

                List<AutoHubPriceChange> vehicles = await GetPriceChangesToProcess(dealerGroup);

                if (vehicles.Count == 0)
                {
                    logger.Info("No price changes to process. Exiting...");
                    return;
                }

                logger.Info($"Found {vehicles.Count} price changes to process");

                // Initialize Chrome driver
                InitializeDriver();

                // Login to AutoHub
                bool loginSuccessful = await LoginAsync();

                if (loginSuccessful)
                {
                    logger.Info($"Login OK, continuing with {vehicles.Count} vehicles to update...");

                    // Update prices
                    await UpdateAutoHubPrices(vehicles);

                    // Verify changes
                    await VerifyAllChanges(vehicles);
                }
                else
                {
                    logger.Error("Failed to login, quitting...");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error in AutoHubPriceChangeJob: {ex.Message}", ex);
                errorMessage = ex.Message;
            }
            finally
            {
                // Clean up
                if (_driver != null)
                {
                    _driver.Quit();
                    _driver = null;
                }

                stopwatch.Stop();
                logger.Info($"AutoHubPriceChangeJob completed in {stopwatch.Elapsed.TotalMinutes:F2} minutes");

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    var emailerService = new EmailerService();
                    await emailerService.SendMail("❌ AutoHub Price Change Job Error", 
                        $"Error occurred during AutoHub price change job: {errorMessage}");
                }
            }
        }

        public async Task<List<AutoHubPriceChange>> GetPriceChangesToProcess(DealerGroupName dealerGroup)
        {
            PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
            RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
            List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

            if (retailers.Count == 0)
            {
                logger.Info("No retailer sites found for dealer group");
                return new List<AutoHubPriceChange>();
            }

            GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup);
            var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

            // There are some changes, so now re-run the vehicle opt-out updater
            var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
            await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

            // Run again in case we just made some optouts
            GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

            List<AutoHubPriceChange> result = todayChangesResponse.totalChanges.ConvertAll(x => new AutoHubPriceChange(x));
            return result;
        }

        private void InitializeDriver()
        {
            try
            {
                var chromeOptions = new ChromeOptions();
                chromeOptions.AddArgument("--no-sandbox");
                chromeOptions.AddArgument("--disable-dev-shm-usage");
                chromeOptions.AddArgument("--disable-gpu");
                chromeOptions.AddArgument("--window-size=1920,1080");
                
                // Add headless mode for production
                // chromeOptions.AddArgument("--headless");

                _driver = new ChromeDriver(chromeOptions);
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(10);
                _driver.Manage().Window.Maximize();

                logger.Info("Chrome driver initialized successfully");
            }
            catch (Exception ex)
            {
                logger.Error($"Failed to initialize Chrome driver: {ex.Message}", ex);
                throw;
            }
        }

        private async Task<bool> LoginAsync()
        {
            try
            {
                logger.Info("Attempting to login to AutoHub...");
                
                _driver.Navigate().GoToUrl(homePageLink);
                Thread.Sleep(3000);

                // Wait for login form to load
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));
                
                // Find username field - update selector based on actual AutoHub login form
                var usernameField = wait.Until(ExpectedConditions.ElementIsVisible(By.Id("username"))); // Update selector
                usernameField.Clear();
                usernameField.SendKeys(ConfigService.AutoHubUsername); // Add to config

                // Find password field - update selector based on actual AutoHub login form
                var passwordField = _driver.FindElement(By.Id("password")); // Update selector
                passwordField.Clear();
                passwordField.SendKeys(ConfigService.AutoHubPassword); // Add to config

                // Click login button - update selector based on actual AutoHub login form
                var loginButton = _driver.FindElement(By.XPath("//button[@type='submit']")); // Update selector
                loginButton.Click();

                Thread.Sleep(5000);

                // Check if login was successful by looking for a specific element that appears after login
                // Update this check based on actual AutoHub interface
                bool loginSuccess = _driver.FindElements(By.XPath("//a[contains(text(), 'Dashboard')]")).Count > 0;

                if (loginSuccess)
                {
                    logger.Info("Successfully logged into AutoHub");
                    return true;
                }
                else
                {
                    logger.Error("Login failed - dashboard not found");
                    return false;
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Login failed with exception: {ex.Message}", ex);
                return false;
            }
        }

        private async Task UpdateAutoHubPrices(List<AutoHubPriceChange> vehicles)
        {
            logger.Info($"Starting to update {vehicles.Count} vehicle prices...");

            foreach (var vehicle in vehicles)
            {
                try
                {
                    logger.Info($"Processing vehicle: {vehicle.Reg}");
                    
                    // Navigate to vehicle management/inventory page
                    // Update URL based on actual AutoHub interface
                    _driver.Navigate().GoToUrl("https://autohub.co.uk/inventory"); 
                    
                    Thread.Sleep(2000);

                    // Search for the vehicle by registration
                    bool vehicleFound = await SearchForVehicle(vehicle);
                    
                    if (vehicleFound)
                    {
                        // Update the price
                        await UpdateVehiclePrice(vehicle);
                        
                        // Save the change result
                        await SaveChangeResult(vehicle);
                        
                        logger.Info($"Successfully updated price for {vehicle.Reg} to {vehicle.Price}");
                    }
                    else
                    {
                        logger.Warn($"Vehicle {vehicle.Reg} not found in AutoHub inventory");
                        vehicle.SavedOk = false;
                        vehicle.SaveError = "Vehicle not found in inventory";
                        await SaveChangeResult(vehicle);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error($"Error updating vehicle {vehicle.Reg}: {ex.Message}", ex);
                    vehicle.SavedOk = false;
                    vehicle.SaveError = ex.Message;
                    await SaveChangeResult(vehicle);
                }
            }
        }

        private async Task<bool> SearchForVehicle(AutoHubPriceChange vehicle)
        {
            try
            {
                // Implement vehicle search logic based on AutoHub interface
                // This is a placeholder - update based on actual AutoHub search functionality
                
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                
                // Find search field and enter registration
                var searchField = wait.Until(ExpectedConditions.ElementIsVisible(By.Id("search"))); // Update selector
                searchField.Clear();
                searchField.SendKeys(vehicle.Reg);
                
                // Click search button
                var searchButton = _driver.FindElement(By.XPath("//button[contains(text(), 'Search')]")); // Update selector
                searchButton.Click();
                
                Thread.Sleep(3000);
                
                // Check if vehicle was found in results
                var vehicleResults = _driver.FindElements(By.XPath($"//td[contains(text(), '{vehicle.Reg}')]")); // Update selector
                
                return vehicleResults.Count > 0;
            }
            catch (Exception ex)
            {
                logger.Error($"Error searching for vehicle {vehicle.Reg}: {ex.Message}", ex);
                return false;
            }
        }

        private async Task UpdateVehiclePrice(AutoHubPriceChange vehicle)
        {
            try
            {
                // Click on the vehicle to edit
                var vehicleRow = _driver.FindElement(By.XPath($"//td[contains(text(), '{vehicle.Reg}')]/..//a[contains(text(), 'Edit')]")); // Update selector
                vehicleRow.Click();
                
                Thread.Sleep(2000);
                
                // Find price field and update it
                var priceField = _driver.FindElement(By.Id("price")); // Update selector based on actual AutoHub form
                priceField.Clear();
                priceField.SendKeys(vehicle.Price.ToString());
                
                // Save the changes
                var saveButton = _driver.FindElement(By.XPath("//button[contains(text(), 'Save')]")); // Update selector
                saveButton.Click();
                
                Thread.Sleep(3000);
                
                vehicle.SavedOk = true;
                vehicle.PriceChanged = true;
                
                logger.Info($"Price updated for {vehicle.Reg} to {vehicle.Price}");
            }
            catch (Exception ex)
            {
                logger.Error($"Error updating price for vehicle {vehicle.Reg}: {ex.Message}", ex);
                vehicle.SavedOk = false;
                vehicle.SaveError = ex.Message;
                throw;
            }
        }

        private async Task VerifyAllChanges(List<AutoHubPriceChange> vehicles)
        {
            logger.Info("Starting verification of price changes...");
            
            foreach (var vehicle in vehicles.Where(v => v.SavedOk))
            {
                try
                {
                    // Implement verification logic
                    // This would involve searching for the vehicle again and checking the price
                    logger.Info($"Verifying price change for {vehicle.Reg}");
                    
                    // Add verification logic here based on AutoHub interface
                    
                }
                catch (Exception ex)
                {
                    logger.Error($"Error verifying vehicle {vehicle.Reg}: {ex.Message}", ex);
                }
            }
        }

        private async Task SaveChangeResult(AutoHubPriceChange vehicle)
        {
            try
            {
                string _connectionString = ConfigService.GetConnectionString(dealerGroup);
                var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
                
                // Create a list with the single vehicle for the update method
                var updates = new List<PricingChangeMinimal>
                {
                    new PricingChangeMinimal
                    {
                        PriceChangeId = vehicle.PriceChangeId,
                        VehicleReg = vehicle.Reg,
                        NewPrice = vehicle.Price,
                        RetailerSiteId = vehicle.RetailerSiteId,
                        IsAutoChange = vehicle.IsAutoPriceChange
                    }
                };
                
                await priceChangesDataAccess.UpdatePriceChangeAutoItems(updates, DateTime.Now);
                
                logger.Info($"Saved change result for {vehicle.Reg}");
            }
            catch (Exception ex)
            {
                logger.Error($"Error saving change result for {vehicle.Reg}: {ex.Message}", ex);
            }
        }
    }
}
