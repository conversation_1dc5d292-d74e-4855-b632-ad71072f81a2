CREATE OR ALTER PROCEDURE [autoprice].[GET_ManualPriceChanges]
(
	@dealerGroupId int,
	@RetailerSiteIds nvarchar(MAX)
	
)


AS
BEGIN

SELECT Value as Id INTO #chosenRetailerSite from STRING_SPLIT(@retailerSiteIds,',') ;
DECLARE @today Date = getDate();


SELECT
  ads.VehicleReg,
  ads.Derivative,
  ads.Make,
  ads.WebSiteStockIdentifier,
  manuals.WasPrice,
  manuals.NowPrice as NewPrice,
  ads.RetailerSite_Id as RetailerSiteId,
  rs.Name as RetailerName,
  manuals.id as PriceChangeId,
  manuals.DateConfirmed,
  0 as IsOptedOutOnDay,
  manuals.Person_Id as ApprovedById,
  rs.ClickDealerFee
  FROM autoprice.PriceChangeManualItems manuals
  INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.Id = manuals.VehicleAdvertSnapshot_Id
  INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
  INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
  INNER JOIN #chosenRetailerSite cr on cr.id = rs.id
  WHERE rs.DealerGroup_Id = @dealerGroupId
  AND manuals.CreatedDate >= @today


DROP TABLE #chosenRetailerSite

END
GO