﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
    public class ReportPriceChangesActionedService
    {

        public async Task SendEmailsForPerson(List<PricingChangeMinimal> changes, string personEmail, string personName,ILog logger, DealerGroupName customer)
        {
            try
            {
                StringBuilder allText = new StringBuilder();
                allText.Append(GenerateSummarySiteTable(changes));
                allText.Append("<br><br><br><br><br><br>");
                allText.Append(GenerateDetailTable(changes));

                var emails = new List<string>() { personEmail };   //personEmail  TODO
                await EmailerService.SendMail(customer, "Spark - Price Changes Actioned", allText.ToString(), null, logger, emails, new List<string>());
                logger.Info($"Sent email for {personName}");
            }

            catch (Exception e)
            {
                logger.Error(e);
            }
        }

      

        private static string GenerateSummarySiteTable(List<PricingChangeMinimal> changes)
        {
            //make summary table
            var summaryTable = new StringBuilder();

            summaryTable.Append("<h2> Price Changes Actioned By Site</h2>");

            SummaryTableHeaderRows(summaryTable);
            foreach (var siteGrouping in changes.ToList().ToLookup(x => x.RetailerName))
            {
                SummaryTableBodyRows(summaryTable, siteGrouping);
            }

            summaryTable.Append(@"</tbody></table>");//close off

            var summaryBodyText = summaryTable.ToString();
            return summaryBodyText;
        }

        private static string GenerateDetailTable(List<PricingChangeMinimal> changes)
        {
            var detailedTableHTML = new StringBuilder();

            detailedTableHTML.Append("<h2> Price Changes Actioned - Detail</h2>");

            foreach (var siteGrouping in changes.ToList().ToLookup(x => x.RetailerName))
            {
                detailedTableHTML.Append($"<h3> {siteGrouping.Key}</h3>");
                DetailTableHeaders(detailedTableHTML);
                DetailTableBodyRows(detailedTableHTML, siteGrouping);
                detailedTableHTML.Append(@"</tbody></table>");//close off
            }

            var bodyText = detailedTableHTML.ToString();
            return bodyText;
        }



        private static void SummaryTableHeaderRows(StringBuilder emailBody)
        {
            emailBody.Append($@"<table><thead>

<tr >
		<th style=""padding:3px 10px; text-align:left"" >Site</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""4"">Price Changes</th>  
	</tr>
	<tr>
		<th style=""min-width: 160px;width: 100px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Succeeded</th>
		<th style=""min-width: 80px;width: 30px;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Failed</th>
		<th style=""min-width: 80px;width: 30px;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Opted Out since</th>
		<th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">TOTAL</th>
		
	</tr>
</thead>
<tbody>");
        }

        private static void SummaryTableBodyRows(StringBuilder emailBody, IGrouping<string, PricingChangeMinimal> siteGrouping)
        {
            int succeeded = 0;
            int failed = 0;
            int optedOutSince = 0;

            foreach (PricingChangeMinimal change in siteGrouping)
            {
                if (change.DateConfirmed !=null ) { succeeded++; }
                else if (change.IsOptedOutOnDay) { optedOutSince++; }
                else { failed++; }
            }

            emailBody.Append($@"<tr style=""font-size:14px;"">

	<td style=""white-space: nowrap; padding:1px 10px;"" > {siteGrouping.Key}</td>
	<td style=""border-left:1px solid #808080; padding:1px 10px;"" >{succeeded}</td>
	<td style=""padding:1px 10px;"" >{failed}</td>
	<td style=""padding:1px 10px;"" >{optedOutSince}</td>
	<td style=""border-left:1px solid #808080; padding:1px 10px;"" >{succeeded + failed + optedOutSince}</td>
</tr>");

        }


        private static void DetailTableHeaders(StringBuilder emailBody)
        {
            emailBody.Append($@"<table><thead>

<tr >
		<th style="" text-align:left; padding:10px"" colspan=""2"">Vehicle</th> 
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Price Change</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" >When actioned</th>  
		<th style=""padding:3px 10px; text-align:left"" >Result</th>  
	</tr>
	<tr>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Reg</th>
		<th style=""min-width: 80px;width: 200px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Description</th>

		<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Was</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Now</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Change</th>
		
		<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
	</tr>
</thead>
<tbody>");
        }


        private static void DetailTableBodyRows(StringBuilder emailBody, IGrouping<string, PricingChangeMinimal> siteGrouping)
        {


            foreach (var priceChange in siteGrouping)
            {
                string changeStyle = string.Empty;
                if (priceChange.TotalChangeValue < -500) { changeStyle = "background:orangered;"; }
                else if (priceChange.TotalChangePercent<= -0.02M) { changeStyle = "background:lightcoral;"; }
                else if (priceChange.TotalChangePercent >= 0.02M) { changeStyle = "background:lightGreen;"; }
                else { changeStyle = "background:lightgoldenrodyellow;"; }

                string whenActioned = priceChange.DateConfirmed != null ? ((DateTime)priceChange.DateConfirmed).ToString("h:mmtt d MMMM") : string.Empty;

                emailBody.Append($@"<tr style=""font-size:14px;"">

	<td style=""white-space: nowrap; padding:1px 10px;"" > {priceChange.VehicleReg}</td>
	<td style="" white-space: nowrap;padding:1px 10px;"" >{priceChange.Derivative}</td>
	
	<td  style="" border-left:1px solid #808080;padding:1px 10px;"" > {FormatAsCurrency((priceChange.WasPrice ?? 0))}</td>
    <td  style=""white-space: nowrap; padding:1px 10px;"" > {FormatAsCurrency(priceChange.NewPrice)}</td>
	<td style=""{changeStyle} white-space: nowrap; border-left:1px solid #808080;padding:1px 10px;"" > {FormatAsCurrency(priceChange.TotalChangeValue)} </td>
	
	<td  style=""white-space: nowrap; border-left:1px solid #808080;padding:1px 10px;"" > {whenActioned}</td>
	<td  style=""white-space: nowrap; padding:1px 10px;"" > {(priceChange.DateConfirmed != null ? "OK" : "Failed")}</td>
</tr>");

            }
        }










        private static string FormatAsCurrency(decimal value)
        {
            string formattedPrice = string.Format("{0:£#,##0}", Math.Abs(value));
            formattedPrice = value < 0 ? "-" + formattedPrice : formattedPrice;
            return formattedPrice;
        }

        private static string FormatAsPercentage(decimal value)
        {
            string formattedPercentage = string.Format("{0:P1}", value);
            return formattedPercentage;
        }

        private static string Format1Dp(decimal value)
        {
            try
            {
                return string.Format("{0:F1}", value);
            }
            catch (Exception)
            {
                { }
                return "err";
            }
        }
        private static string Format0Dp(decimal value)
        {
            return string.Format("{0:F0}", value);
        }

    }
}
