import { Component, Input, OnInit } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
   ColDef,
   DomLayoutType,
   GridApi,
   GridOptions,
   GridReadyEvent,
   ICellRendererParams,
   ModelUpdatedEvent,
   RowClassParams,
   RowClickedEvent,
} from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { CPHColDef } from "src/app/model/CPHColDef";
import { CPHColGroupDef } from "src/app/model/CPHColGroupDef";
import { SameModelAdvert } from "src/app/model/SameModelAdvert";
import { SimpleExampleItem } from "src/app/model/SimpleExampleItem";
import { StatsVehicle } from "src/app/model/StatsVehicle";
import { StockProfileItem } from "src/app/model/StockProfileItem";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { ConstantsService } from "src/app/services/constants.service";
import { localeEs } from "src/environments/locale.es.js";
import { StockProfilerPageComponentType } from "../stockProfiler.component";
import { ChosenStockProfilerLayout, StockProfilerService } from "../stockProfiler.service";

//This describes the properties that a service must commit to having, when it's used with this component.
//In any service that wants to use this compnent, it must implement this interface using this syntax:
//   export class MyCoolService implements MyComponentParams  { etc.
export interface StockProfilerTableParams {
   gridRef: StockProfilerTableComponent;
   stockProfileItems: StockProfileItem[];
   dealWithFilteredItems: (filteredItems: StockProfileItem[], callingComponent: StockProfilerPageComponentType) => void;
}

@Component({
   selector: "stockProfilerTable",
   templateUrl: "./stockProfilerTable.component.html",
   styleUrls: ["./stockProfilerTable.component.scss"],
})
export class StockProfilerTableComponent implements OnInit {
   @Input() tableParams: StockProfilerTableParams;
   gridOptions: GridOptions;
   gridApi: GridApi;
   indicateNewData: boolean;
   domLayout: DomLayoutType = "normal";
   gridColumnApi: any;

   constructor(
      public gridHelpersService: AGGridMethodsService,
      public constantsService: ConstantsService,
      public cphPipe: CphPipe,
      private modalService: NgbModal,
      private columnTypesService: ColumnTypesService,
      private stockProfilerService: StockProfilerService
   ) {}

   ngOnInit(): void {
      this.setGridDefinitions();
   }

   ngOnDestroy() {
      this.tableParams.gridRef = null;
   }

   setGridDefinitions() {
      this.gridOptions = {
         getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
         getLocaleText: (params: any) =>
            this.constantsService.currentLang == "es"
               ? localeEs[params.key] || params.defaultValue
               : params.defaultValue,
         defaultColDef: {
            resizable: true,
            sortable: true,
            cellRendererParams: {
               suppressCount: true,
               suppressTotal: true,
            },
            filterParams: {
               applyButton: false,
               clearButton: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            autoHeight: true,
            floatingFilter: false,
         },
         getRowClass: (params) => {
            return this.rowClassGetter(params);
         },
         groupDefaultExpanded: 0,
         suppressRowGroupHidesColumns: true,

         suppressAggFuncInHeader: true,
         columnTypes: {
            ...this.columnTypesService.provideColTypes([]),
         },
         onRowClicked: (params) => this.onRowClick(params),
         rowData: this.tableParams.stockProfileItems, //.filter(x=>x.RetailerSiteName == 'Volvo Cars Oxford'),
         columnDefs: this.provideColDefs(),
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         onFilterChanged: (event) => this.onFilterChanged(event),
         //onModelUpdated: (event: ModelUpdatedEvent) => this.gridApi?.sizeColumnsToFit(),
         getRowHeight: (params) => {
            if (params.node.rowPinned) {
               return this.gridHelpersService.getRowPinnedHeight();
            } else {
               return this.gridHelpersService.getStandardHeight();
            }
         },
      };
   }

   provideColDefs(): (CPHColDef | CPHColGroupDef)[] {
      const regularColsWidth = 100;
      const maxWidth = 160;

      const results = [
         ...this.getSummaryDimensions(regularColsWidth, maxWidth),
         {
            headerName: "Historic sales rate",
            children: [
               {
                  headerName: "Monthly Sales Rate (units)",
                  aggFunc: "sum",
                  maxWidth: maxWidth,
                  colId: "MonthlySalesRate",
                  field: "MonthlySalesRate",
                  type: "number1dp",
                  width: regularColsWidth,
               },
               {
                  headerName: "Av Stock Level (units)",
                  //suppressHeaderAggregation: true,
                  aggFunc: "sum",
                  maxWidth: maxWidth,
                  colId: "AvgStockLevel",
                  field: "AvgStockLevel",
                  type: "number",
                  width: regularColsWidth,
               },
               {
                  headerName: "Stock Cover (days)",
                  //suppressHeaderAggregation: true,
                  valueGetter: (params) => {
                     // Group row: use aggData
                     const node = params.node;
                     const avStock = node.group ? node.aggData?.AvgStockLevel : params.data?.AvgStockLevel;
                     const salesRate = node.group ? node.aggData?.MonthlySalesRate : params.data?.MonthlySalesRate;

                     if (avStock && salesRate) {
                        return (avStock / salesRate) * 30;
                     }
                     return null;
                  },
                  maxWidth: maxWidth,
                  colId: "AvgStockLevel",
                  type: "number",
                  width: regularColsWidth,
               },
            ],
         },
         {
            headerName: "Current Stock",
            children: [
               {
                  headerName: "Current Stock (units)",
                  aggFunc: "sum",
                  maxWidth: maxWidth,
                  colId: "StockLevel",
                  field: "StockLevel",
                  type: "number",
                  width: regularColsWidth,
               },
               {
                  headerName: "Current Stock Cover (days)",
                  valueGetter: (params) => {
                     // Group row: use aggData
                     const node = params.node;
                     const currentStock = node.group ? node.aggData?.StockLevel : params.data?.StockLevel;
                     const salesRate = node.group ? node.aggData?.MonthlySalesRate : params.data?.MonthlySalesRate;

                     if (currentStock && salesRate) {
                        return (currentStock / salesRate) * 30;
                     }
                     return null;
                  },
                  maxWidth: maxWidth,
                  colId: "CurrentStockCover",
                  type: "number",
                  width: regularColsWidth,
               },
            ],
         },
      ];

      return results;
   }
   getSummaryDimensions(regularColsWidth: number, maxWidth: number) {
      const siteCol = {
         headerName: "Site",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "RetailerSiteName",
         field: "RetailerSiteName",
         type: "labelSetFilter",
         width: 100,
      };
      const modelCol = {
         headerName: "Model",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "ModelClean",
         field: "ModelClean",
         type: "labelSetFilter",
         width: regularColsWidth,
      };

      const fuelCol = {
         headerName: "Fuel Type",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "FuelType",
         field: "FuelType",
         type: "labelSetFilter",
         width: regularColsWidth,
      };
      const bodyCol = {
         headerName: "Body Type",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "BodyType",
         field: "BodyType",
         type: "labelSetFilter",
         width: regularColsWidth,
      };
      const drivetrainCol = {
         headerName: "Drivetrain",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "Drivetrain",
         field: "Drivetrain",
         type: "labelSetFilter",
         width: regularColsWidth,
      };
      const ageBandCol = {
         headerName: "Age Band",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "AgeBand",
         field: "AgeBand",
         type: "labelSetFilter",
         width: regularColsWidth,
      };
      const makeCol = {
         headerName: "Make",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "Make",
         field: "Make",
         type: "labelSetFilter",
         width: regularColsWidth,
      };
      const pricebandCol = {
         headerName: "Price Band",
         rowGroup: true,
         hide: true,
         maxWidth: maxWidth,
         colId: "PriceBand",
         field: "PriceBand",
         type: "labelSetFilter",
         width: regularColsWidth,
      };

      if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Site) {
         return [siteCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Model) {
         return [modelCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.SiteModel) {
         return [siteCol, modelCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.ModelSite) {
         return [modelCol, siteCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.FuelType) {
         return [fuelCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.BodyType) {
         return [bodyCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.AgeBand) {
         return [ageBandCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Drivetrain) {
         return [drivetrainCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Make) {
         return [makeCol];
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.PriceBand) {
         return [pricebandCol];
      } else {
         return [siteCol];
      }
   }

   rowClassGetter(params: RowClassParams<any, any>): string[] {
      const item: SimpleExampleItem = params.data;
      return item?.isChosen ? ["brightHighlight"] : [];
   }

   getImage(params: ICellRendererParams) {
      const row: SameModelAdvert = params.data;

      if (!row || !row?.ImageUrl) return "";
      return `<img style="height: 50px; width: 100%;" src=${row.ImageUrl} />`;
   }

   onGridReady(event: GridReadyEvent) {
      this.gridApi = event.api;
      this.gridColumnApi = event.columnApi;
      //this.gridApi.sizeColumnsToFit();
      this.tableParams.gridRef = this;
      this.stockProfilerService.gridRef = this;
   }

   dealWithNewData(data: StatsVehicle[]) {
      this.gridApi.setRowData(data);
      //this.gridApi.sizeColumnsToFit();
      this.indicateNewData = true;
      setTimeout(() => {
         this.indicateNewData = false;
      }, 1000);
   }

   updateColumns() {
      const colDefs = this.provideColDefs();
      this.gridApi.setColumnDefs(colDefs);

      if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Site) {
         this.gridColumnApi.setRowGroupColumns(["RetailerSiteName"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Model) {
         this.gridColumnApi.setRowGroupColumns(["ModelClean"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.SiteModel) {
         this.gridColumnApi.setRowGroupColumns(["RetailerSiteName", "ModelClean"]);
         this.gridOptions.groupDefaultExpanded = 1;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.ModelSite) {
         this.gridColumnApi.setRowGroupColumns(["ModelClean", "RetailerSiteName"]);
         this.gridOptions.groupDefaultExpanded = 1;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.FuelType) {
         this.gridColumnApi.setRowGroupColumns(["FuelType"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.BodyType) {
         this.gridColumnApi.setRowGroupColumns(["BodyType"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.AgeBand) {
         this.gridColumnApi.setRowGroupColumns(["AgeBand"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Drivetrain) {
         this.gridColumnApi.setRowGroupColumns(["Drivetrain"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.Make) {
         this.gridColumnApi.setRowGroupColumns(["Make"]);
         this.gridOptions.groupDefaultExpanded = 0;
      } else if (this.stockProfilerService.chosenLayout === ChosenStockProfilerLayout.PriceBand) {
         this.gridColumnApi.setRowGroupColumns(["PriceBand"]);
         this.gridOptions.groupDefaultExpanded = 0;
      }

      //now do this
      this.gridApi.refreshClientSideRowModel("group");
   }

   onFilterChanged(event: any) {
      // Get the filtered rows

      const filteredNodes = [];
      this.gridApi.forEachNodeAfterFilter((node) => {
         filteredNodes.push(node);
      });
      const filteredItems = filteredNodes.map((node) => node.data); // Get data from each node

      // Call the parent service method and pass the filtered items
      this.tableParams.dealWithFilteredItems(filteredItems, StockProfilerPageComponentType.grid);
   }

   onRowClick(params: RowClickedEvent<any, any>): void {
      const item: SimpleExampleItem = params.data;
      item.isChosen = !item.isChosen;
      params.node.setData(item);
      this.tableParams.dealWithFilteredItems(this.tableParams.stockProfileItems, StockProfilerPageComponentType.grid);
   }
}
