﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Model.ViewModels.RRG;
using log4net;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Formats.Tar;
using System.Globalization;
using System.Linq;
using System.Net.Http.Headers;
using System.Security.Policy;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Net.WebRequestMethods;
using System.Linq;
using System.Collections.Generic;
using System;
using System.Threading.RateLimiting;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using log4net.Repository.Hierarchy;
//using MoreLinq;

namespace CPHI.Spark.DataAccess.AutoPrice
{

   public interface IAutoTraderCompetitorClient
   {
      Task<VehicleListing> GetCompetitorByAdvertiser(string competitorLink, int pageNumber, string _bearerToken, ILog? logger);
      Task<VehicleListing> GetCompetitorAnalysis(GetCompetitorAnalysisParams parms, string _bearerToken);
      Task<VehicleListing> GetCompetitorNew(CompetitorSearchParams parms, ILog? logger);
      Task<VehicleListing> GetCompetitorWithPageNumberNew(CompetitorSearchParams parms, int pageNumber, ILog? logger, VehicleAdvertWithRating advert);
   }



   public class AutoTraderCompetitorClient : IAutoTraderCompetitorClient
   {

      private readonly HttpClient _httpClient;
      private string atApiKey;
      private string atApiSecret;
      private string atBaseURL;

      private readonly IAutoTraderApiTokenClient autoTraderApiTokenClient;
      private readonly RateLimiter rateLimiter;
      private readonly ILog _logger;



      public AutoTraderCompetitorClient(IHttpClientFactory httpClientFactory, string atApiKeyIn, string atApiSecretIn, string atBaseURLIn)
      {
         _httpClient = httpClientFactory.CreateClient();


         atApiKey = atApiKeyIn;
         atApiSecret = atApiSecretIn;
         atBaseURL = atBaseURLIn;

         autoTraderApiTokenClient = new AutoTraderApiTokenClient(httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         rateLimiter = AutotraderRateLimiter.StockLimiter;
      }



      public async Task<VehicleListing> GetCompetitorByAdvertiser(string competitorLink, int pageNumber, string _bearerToken, ILog? logger)
      {

         //Update compititor link
         competitorLink += $"&page={pageNumber}";

         var request = new HttpRequestMessage(HttpMethod.Get, competitorLink);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken);

         //Console.WriteLine($"seconds is {DateTime.Now.Second}, calling {TempExtractRegistration(competitorLink)} page {pageNumber}");
         var response = await SendRateLimitedRequestAsync(request, logger);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResult = responseContent;
            try
            {
               VehicleListing vehicles = System.Text.Json.JsonSerializer.Deserialize<VehicleListing>(jsonResult);
               vehicles.errorMessage = string.Empty;
               return vehicles;

            }
            catch (Exception ex)
            {
               { }
               return new VehicleListing()
               {
                  totalResults = 0,
                  results = new List<AutoTraderVehicleListing>(),
                  errorMessage = $"Error {ex.Message}"
               };
            }
         }
         else
         {
            //throw new Exception($"Unable to retrieve data: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            if (logger != null)
            {
               logger.Info($"Received response code '{response.StatusCode}' when calling: {competitorLink}  ");
            }

            //throw new Exception($"Unable to retrieve data: {response.StatusCode}");
            return new VehicleListing()
            {
               totalResults = 0,
               results = new List<AutoTraderVehicleListing>(),
               errorMessage = $"Non success code on  {response.StatusCode} |  {responseContent}"
            };
         }
      }


      public async Task<VehicleListing> GetCompetitorAnalysis(GetCompetitorAnalysisParams parms, string _bearerToken)
      {

         var advertiserId = parms.RetailerSiteRetailerId;
         var searchId = parms.WebsiteSearchIdentifier;
         var minPlate = parms.UserChoiceParams.MinPlate;
         var maxPlate = parms.UserChoiceParams.MaxPlate;
         var radius = parms.UserChoiceParams.Radius;
         var postcode = parms.UserChoiceParams.Postcode;


         string competitorLink = string.Empty;
         try
         {
            competitorLink = await GetCompetitorLink(_bearerToken, advertiserId, searchId);
         }
         catch (Exception ex)
         {
            return new VehicleListing()
            {
               errorMessage = $"Error on GetCompetitorLink {ex.Message}"
            };
         }

         if (string.IsNullOrEmpty(competitorLink))
         {
            return new VehicleListing()
            {
               errorMessage = $"GetCompetitorLink Not Found"
            };
         }


         var uri = new Uri(competitorLink);

         string baseUri = uri.GetLeftPart(UriPartial.Path);
         var queryDict = HttpUtility.ParseQueryString(uri.Query);


         queryDict["advertiserId"] = RetailerIdSwapService.ProvideUpdatedId(advertiserId).ToString();
         queryDict["!insuranceWriteoffCategory"] = "";
         queryDict["features"] = "false";
         queryDict["check"] = "false";
         queryDict["history"] = "false";
         queryDict["minPlate"] = minPlate.ToString();
         queryDict["maxPlate"] = maxPlate.ToString();
         queryDict["distance"] = radius.ToString();
         queryDict["postcode"] = postcode.ToString();

         queryDict.Remove("minEnginePowerBHP");
         queryDict.Remove("maxEnginePowerBHP");
         queryDict.Remove("minBadgeEngineSizeLitres");
         queryDict.Remove("maxBadgeEngineSizeLitres");

         if (parms.UserChoiceParams.FuelType.Count > 0)
         {
            queryDict.Remove("standardFuelType");
            foreach (var x in parms.UserChoiceParams.FuelType)
            {
               queryDict.Add("standardFuelType", x);
            }
         }

         if (parms.UserChoiceParams.Trim.Count > 0)
         {
            queryDict.Remove("standardTrim");
            foreach (var x in parms.UserChoiceParams.Trim)
            {
               queryDict.Add("standardTrim", x);
            }
         }

         if (parms.UserChoiceParams.BodyType.Count > 0)
         {
            queryDict.Remove("standardBodyType");
            foreach (var x in parms.UserChoiceParams.BodyType)
            {
               queryDict.Add("standardBodyType", x);
            }
         }

         if (parms.UserChoiceParams.Doors.Count > 0)
         {
            queryDict.Remove("doors");
            foreach (var x in parms.UserChoiceParams.Doors)
            {
               queryDict.Add("doors", x.ToString());
            }
         }

         if (parms.UserChoiceParams.TransmissionType.Count > 0)
         {
            queryDict.Remove("standardTransmissionType");
            foreach (var x in parms.UserChoiceParams.TransmissionType)
            {
               queryDict.Add("standardTransmissionType", x);
            }
         }

         if (parms.UserChoiceParams.Drivetrain.Count > 0)
         {
            queryDict.Remove("standardDrivetrain");
            foreach (var x in parms.UserChoiceParams.Drivetrain)
            {
               queryDict.Add("standardDrivetrain", x);
            }
         }

         competitorLink = $"{baseUri}?{queryDict.ToString()}";

         /*
         //Update competitor link
         competitorLink += $"&advertiserId={RetailerIdSwapService.ProvideUpdatedId(advertiserId)}&!insuranceWriteoffCategory&features=false&check=false&history=false";

         var splitCompetitorLink = competitorLink.Split('&').ToList();

         var minPlateInURLIndex = splitCompetitorLink.FindIndex(c => c.Contains("minPlate"));
         int minPlateValue = int.Parse(splitCompetitorLink[minPlateInURLIndex].Split('=')[1]);
         splitCompetitorLink.RemoveAt(minPlateInURLIndex);

         var maxPlateInURLIndex = splitCompetitorLink.FindIndex(c => c.Contains("maxPlate"));
         int maxPlateValue = int.Parse(splitCompetitorLink[maxPlateInURLIndex].Split('=')[1]);
         splitCompetitorLink.RemoveAt(maxPlateInURLIndex);

         splitCompetitorLink.Add($"minPlate={minPlate}");
         splitCompetitorLink.Add($"maxPlate={maxPlate}");
         splitCompetitorLink.Add($"distance={radius}");
         splitCompetitorLink.Add($"postcode={postcode}");

         competitorLink = string.Join('&', splitCompetitorLink);
         */


         try
         {
            //return vehicles;
            var firstPageResults = await GetCompetitorByAdvertiser(competitorLink, 1, _bearerToken, _logger);

            int pagesCount = (int)Math.Ceiling((double)firstPageResults.totalResults / 20);
            if (pagesCount > 1)
            {
               int pagesCountToUse = Math.Min(pagesCount, 10);
               for (int i = 2; i <= pagesCountToUse; i++)
               {
                  var nextPageResults = await GetCompetitorByAdvertiser(competitorLink, i, _bearerToken, _logger);
                  firstPageResults.results.AddRange(nextPageResults.results);
               }
            }

            return firstPageResults;
         }
         catch (Exception ex)
         {
            //throw new Exception(ex.Message, ex);
            return new VehicleListing()
            {
               totalResults = 0,
               results = new List<AutoTraderVehicleListing>(),
               errorMessage = $"Error {ex.Message}",
            };
         }
      }


      public async Task<VehicleListing> GetCompetitorWithPageNumberNew(CompetitorSearchParams parms, int pageNumber, ILog? logger, VehicleAdvertWithRating advert)
      {

         string competitorLink = BuildOrUpdateCompetitorLink(parms);
         var res = await GetCompetitorByAdvertiser(competitorLink, pageNumber, parms.token.AccessToken, logger);
         res.Advert = advert;
         if (res.errorMessage != string.Empty)
         {
            if (logger != null)
            {
               logger.Error(res.errorMessage);
            }
         }
         return res;

      }

      //New method, created 12 October.  Updated, now runs in parallel.  


      public async Task<VehicleListing> GetCompetitorNew(CompetitorSearchParams parms, ILog? logger)
      {
         try
         {
            //renew token if required
            var token = await autoTraderApiTokenClient.CheckExpiryAndRegenerate(parms.token);

            string competitorLink = BuildOrUpdateCompetitorLink(parms);

            VehicleListing firstPageResult = await GetCompetitorByAdvertiser(competitorLink, 1, token.AccessToken, logger);

            if (firstPageResult != null && firstPageResult.totalResults > 20) // Check if there are more pages
            {
               var nextPageRequests = new List<Task<VehicleListing>>(); // To get additional pages to fetch
               int totalPages = (int)Math.Ceiling((double)firstPageResult.totalResults / 20);
               totalPages = totalPages > 10 ? 10 : totalPages; // Limit to 10 pages

               // Queue up additional pages for this advert
               for (int nextPageNumber = 2; nextPageNumber <= totalPages; nextPageNumber++)
               {
                  try
                  {
                     Task<VehicleListing> newTask = GetCompetitorByAdvertiser(competitorLink, nextPageNumber, token.AccessToken, logger);
                     nextPageRequests.Add(newTask);
                  }
                  catch (Exception ex)
                  {
                     continue;
                  }
               }

               //Execute them all and put results into firstPageResult
               if (nextPageRequests.Any())
               {
                  var allResults = await Task.WhenAll(nextPageRequests);
                  foreach (var result in allResults)
                  {
                     firstPageResult.results.AddRange(result.results);
                  }
               }
            }


            return firstPageResult;
         }
         catch (Exception ex)
         {
            if (logger != null)
            {
               logger.Error($"Error on GetCompetitorLink {ex.Message}");
            }
            return new VehicleListing()
            {
               totalResults = 0,
               results = new List<AutoTraderVehicleListing>(),
               errorMessage = $"Error {ex.Message}",
               //MinPlate = minPlateValue,
               //MaxPlate = maxPlateValue
            };
         }


         //throw new NotImplementedException();
      }

      public string BuildOrUpdateCompetitorLink(CompetitorSearchParams parms)
      {
         string competitorLink;
         if (parms.CompetitorLink != null)
         {
            //we already have one, just need to update it
            competitorLink = UpdateCompetitorUrl(parms.CompetitorLink, parms);
         }
         else
         {
            competitorLink = BuildCompetitorSearchUrl(parms);
         }

         return competitorLink;
      }




      public string UpdateCompetitorUrl(string competitorLink, CompetitorSearchParams searchParams)
      {
         // Parse the existing URL into a Uri object
         var uri = new Uri(competitorLink);
         var query = System.Web.HttpUtility.ParseQueryString(uri.Query);

         // Update the properties if they are provided
         query["advertiserId"] = searchParams.advertiserId.ToString();
         query["postcode"] = searchParams.postcode;
         query["distance"] = searchParams.radius.ToString();
         query["minPlate"] = searchParams.minPlate.ToString();
         query["maxPlate"] = searchParams.maxPlate.ToString();

         query["!insuranceWriteoffCategory"] = string.Empty;

         // Reconstruct the updated URL
         var uriBuilder = new UriBuilder(uri)
         {
            Query = query.ToString()
         };

         return uriBuilder.ToString();
      }


      public string BuildCompetitorSearchUrl(CompetitorSearchParams searchParams)
      {
         if (searchParams.standardMake == null) { throw new Exception("standardMake is null"); }
         if (searchParams.standardModel == null) { throw new Exception("standardModel is null"); }
         //if (searchParams.standardTransmissionType == null) { throw new Exception("standardTransmissionType is null"); }
         //if (searchParams.standardFuelType == null) { throw new Exception("standardFuelType is null"); }
         //if (searchParams.standardBodyType == null) { throw new Exception("standardBodyType is null"); }
         //if (searchParams.standardDrivetrain == null) { throw new Exception("standardDrivetrain is null"); }

         if (searchParams.postcode == null) { throw new Exception("postcode is null"); }
         if (searchParams.radius == null) { throw new Exception("radius is null"); }

         if (searchParams.minPlate == null) { throw new Exception("minPlate is null"); }
         if (searchParams.maxPlate == null) { throw new Exception("maxPlate is null"); }

         // Build the query parameters
         var queryParams = new List<string>
            {
                "searchType=competitor",
                "valuations=true",
                $"advertiserId={searchParams.advertiserId}",
                $"registration!={searchParams.selfRegToExclude}",
                $"standardMake={Uri.EscapeDataString(searchParams.standardMake)}",
                $"standardModel={Uri.EscapeDataString(searchParams.standardModel)}",
                
                //$"standardBodyType={Uri.EscapeDataString(searchParams.standardBodyType)}",
                //$"standardDrivetrain={Uri.EscapeDataString(searchParams.standardDrivetrain)}",

                "features=false",
                "check=false",
                "!insuranceWriteoffCategory",
                "history=false",
                $"postcode={searchParams.postcode}",
                $"distance={searchParams.radius}",
                $"minPlate={searchParams.minPlate}",
                $"maxPlate={searchParams.maxPlate}",
            };

         //add standardTransmissionType
         if (searchParams.standardTransmissionType != null)
         {
            queryParams.Add($"standardTransmissionType={Uri.EscapeDataString(searchParams.standardTransmissionType)}");
         }

         //add standardFuelType
         if (searchParams.standardFuelType != null)
         {
            queryParams.Add($"standardFuelType={Uri.EscapeDataString(searchParams.standardFuelType)}");
         }
            
         //add standardDrivetrain
         if (searchParams.standardDrivetrain != null)
         {
            queryParams.Add($"standardDrivetrain={Uri.EscapeDataString(searchParams.standardDrivetrain)}");
         }

         //add standardBodyType
         if (searchParams.standardBodyType != null)
         {
            queryParams.Add($"standardBodyType={Uri.EscapeDataString(searchParams.standardBodyType)}");
         }

         //add doors
         if (searchParams.doors != null)
         {
            queryParams.Add($"doors={searchParams.doors}");
         }

         //add standardTrim
         if (searchParams.standardTrim != null)
         {
            queryParams.Add($"standardTrim={Uri.EscapeDataString(searchParams.standardTrim)}");
         }


         if (searchParams.maxBadgeEngineSizeLitres != null)  //could be null if electric car
         {
            queryParams.Add($"minBadgeEngineSizeLitres={searchParams.minBadgeEngineSizeLitres}");
            queryParams.Add($"maxBadgeEngineSizeLitres={searchParams.maxBadgeEngineSizeLitres}");
         }
         if (searchParams.maxEnginePowerBHP != null)
         {
            queryParams.Add($"maxEnginePowerBHP={searchParams.maxEnginePowerBHP}");
         }

         // Concatenate the base URL with the query parameters
         return $"{atBaseURL}/stock?{string.Join("&", queryParams)}";
      }






      private async Task<string> GetCompetitorLink(string _bearerToken, int advertiserId, string searchId)
      {
         string url = $"https://api.autotrader.co.uk/stock?"
         + $"advertiserId={RetailerIdSwapService.ProvideUpdatedId(advertiserId)}"
         + $"&searchId={searchId}"
         + "&competitors=true"
         + "&vehicle=false"
         + "&advertiser=false"
         + "&adverts=false"
         + "&metadata=false"
         + "&features=false"
         + "&media=false"
         + "&check=false"
         //+ "&!insuranceWriteoffCategory"
         + "&history=false";


         var request = new HttpRequestMessage(HttpMethod.Get, url);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken);

         var response = await SendRateLimitedRequestAsync(request, null);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResult = responseContent;
            try
            {
               VehicleListing vehicles = System.Text.Json.JsonSerializer.Deserialize<VehicleListing>(jsonResult);
               vehicles.errorMessage = string.Empty;
               var firstResult = vehicles.results.FirstOrDefault();
               if (firstResult != null && firstResult.links != null)
               {
                  return vehicles.results.First().links.competitor.href;

               }
               else
               {
                  return string.Empty;
               }
            }
            catch (Exception ex)
            {
               { }
               throw;
               //throw new Exception(ex.Message, ex);
            }
         }
         else
         {
            //throw new Exception($"Unable to retrieve data: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            throw new Exception(responseContent);
         }
      }












      public async Task<VehicleListing> GetCompetitorVehicle(int page, int advertiserId, string derivativeId, int distance, string postcode, int minPlate, int maxPlate, string token, string autotraderBaseURL)
      {
         //this is what the scenarios columns is using

         string url = $"{autotraderBaseURL}/stock?page={page}"
        + $"&vehicleMetrics=true"
        + $"&advertiserId={RetailerIdSwapService.ProvideUpdatedId(advertiserId)}"
        + $"&derivativeId={derivativeId}"
        + $"&distance={distance}"
        + $"&postcode={postcode}"
        + $"&searchType=competitor"
        + $"&!insuranceWriteoffCategory"
        + $"&minPlate={minPlate}"
        + $"&maxPlate={maxPlate}"
        + $"&valuations=true";



         var request = new HttpRequestMessage(HttpMethod.Get, url);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

         var response = await SendRateLimitedRequestAsync(request, null);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            try
            {
               VehicleListing vehicles = System.Text.Json.JsonSerializer.Deserialize<VehicleListing>(responseContent);
               vehicles.errorMessage = string.Empty;

               return vehicles;
            }
            catch (Exception ex)
            {
               return new VehicleListing()
               {
                  totalResults = 0,
                  results = new List<AutoTraderVehicleListing>(),
                  errorMessage = $"Error on derivativeid {derivativeId} {ex.Message}"
               };
            }
         }
         else
         {
            //throw new Exception($"Unable to retrieve data: {response.StatusCode}");
            return new VehicleListing()
            {
               totalResults = 0,
               results = new List<AutoTraderVehicleListing>(),
               errorMessage = $"Non success code on derivativeId {derivativeId} {response.StatusCode}"
            };
         }

      }





      private async Task<HttpResponseMessage> SendRateLimitedRequestAsync(HttpRequestMessage request, ILog? logger, CancellationToken cancellationToken = default)
      {
         // First, acquire a permit from the concurrency limiter to ensure we don't exceed 200 concurrent requests
         await AutotraderRateLimiter.ConcurrencyLimiter.WaitAsync(cancellationToken);

         try
         {
            // Then, acquire a permit from the rate limiter to ensure we don't exceed the rate limit
            using (RateLimitLease lease = await rateLimiter.AcquireAsync(1, cancellationToken))
            {
               if (!lease.IsAcquired)
               {
                  // Handle rejection (e.g., throw RateLimiterRejectedException)
                  throw new TimeoutException($"Rate limit permit could not be acquired for request to {request.RequestUri}.");
               }

               // Only send if lease was acquired
               //if (logger != null)
               //{
               //   logger.Info($"Request to {request.RequestUri}");
               //}
               return await _httpClient.SendAsync(request, cancellationToken);
            }
         }
         finally
         {
            // Always release the concurrency limiter permit, even if an exception occurs
            AutotraderRateLimiter.ConcurrencyLimiter.Release();
         }
      }
   }
}
