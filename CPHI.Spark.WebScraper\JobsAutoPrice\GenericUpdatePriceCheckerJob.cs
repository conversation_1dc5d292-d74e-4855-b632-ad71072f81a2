﻿using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using log4net;

using System.Data;
using CPHI.Repository;
using CPHI.Spark.Model;
using System.Threading.Tasks;
using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.Services;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.WebScraper.ViewModel;
using CPHI.Spark.BusinessLogic.AutoPrice;
using System.Text;
using OfficeOpenXml.ConditionalFormatting;
using static CPHI.Spark.Model.ViewModels.AutoPricing.RetailCustomerLatestViewResponseResult;


namespace CPHI.Spark.WebScraper.Jobs
{
   [DisallowConcurrentExecution]
   public class GenericUpdatePriceCheckerJob : IJob
   {
      private readonly IDapper dapper = new Dapperr();
      private static readonly ILog Logger = LogManager.GetLogger(typeof(GenericUpdatePriceCheckerJob));

      public CPHIDbContext db;

      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         try
         {
            //---------------------------------------------------------------------------------------------------

            //Loop through all dealergroups and get results
            string emailBody = "<html><body>";
            int pendingCount = 0;


            //Get list of the all dealergroups
            foreach (var dealerGroup in Enum.GetValues(typeof(DealerGroupName)).Cast<DealerGroupName>())
            {
               //if (dealerGroup == DealerGroupName.Vindis)
               //{
                  List<PricingChangeMinimal> overdueChanges = (await GetPriceChangesToProcess(dealerGroup)).overdueChanges.OrderBy(x => x.RetailerName).ToList();
                  pendingCount += overdueChanges.Count();
                  emailBody += GenerateHtmlTableForOutlook(dealerGroup, overdueChanges);
               //}
            }

            emailBody += "</body></html>";


            //Build Email Message

            EmailerService eService = new EmailerService();
            if (pendingCount > 0)
            {
               await eService.SendMail("❌ FAILURE: Pending Price Changes", emailBody);
            }
            else
            {
               await eService.SendMail("✔️ SUCCESS: Pending Price Changes", emailBody);
            }
         }
         catch (Exception ex)
         {
            EmailerService eService = new EmailerService();
            await eService.SendMail("❌ FAILURE: Pending Price Changes - Exception", ex.Message + Environment.NewLine + ex.StackTrace);
         }


}




      private async Task<GetTodayChangesResponse> GetPriceChangesToProcess(DealerGroupName dealerGroup)
      {
         PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         if (retailers.Count == 0)
         {
            return new GetTodayChangesResponse()
            {
               overdueChanges = new List<PricingChangeMinimal>()
            };
         }

         bool includeUnPublished = retailers.First().IncludeUnPublishedAds;
         GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup);
         var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         return todayChangesFirstPass;

      }

      public static string GenerateHtmlTableForOutlook<T>(DealerGroupName dealerGroup, List<T> items)
      {
         if (items == null || !items.Any())
            return $"<h1> {dealerGroup}</h1><table><tr><td>No pending changes</td></tr></table>";

         var properties = typeof(T).GetProperties();
         var propertiesToInclude = new List<string>() { "RetailerName", "WhenToActionChangesEachDay", "VehicleReg", "AdvertId", "WasPrice", "NewPrice", "TotalChangeValue", "Status" };

         var sb = new StringBuilder();

         sb.Append($"<h1> {dealerGroup}({items.Count()}) </h1><table>");

         // Header
         sb.Append("<thead><tr>");
         foreach (var prop in properties)
         {
            if (!propertiesToInclude.Contains(prop.Name))
            {
               continue;
            }

            // Use &nbsp; to force non-breaking
            var header = System.Net.WebUtility.HtmlEncode(prop.Name).Replace(" ", "&nbsp;");
            sb.AppendFormat($"<th style='white-space: nowrap; min-width: 160px;width: 100px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080'>{header}</th>");
         }
         sb.Append("</tr></thead>");

         // Rows
         sb.Append("<tbody>");
         foreach (var item in items)
         {
            sb.Append("<tr>");
            foreach (var prop in properties)
            {
               if (!propertiesToInclude.Contains(prop.Name))
               {
                  continue;
               }

               var value = prop.GetValue(item, null)?.ToString() ?? "";
               sb.AppendFormat($"<td style='white-space: nowrap;'>{System.Net.WebUtility.HtmlEncode(value)}</td>");
            }
            sb.Append("</tr>");
         }
         sb.Append("</tbody>");

         sb.Append("</table>");
         return sb.ToString();
      }









   }


}
