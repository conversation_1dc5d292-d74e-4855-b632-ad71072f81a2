import { StatsDaysListed } from "./StatsDaysListed";
import { StatsPerformanceRating } from "./StatsPerformanceRating";
import { StatsRetailRating } from "./StatsRetailRating";
import { StatsVsStrategyPrice } from "./StatsVsStrategyPrice";



export class StockProfileItem {

  constructor (itemIn: StockProfileItemDTO) {

    this.RetailerSiteId = itemIn.RetailerSiteId;
    this.RetailerSiteName = itemIn.RetailerSiteName;
    this.ModelClean = itemIn.ModelClean;
    this.PriceBand = itemIn.PriceBand;
    this.AgeBand = itemIn.AgeBand;
    this.Drivetrain = itemIn.Drivetrain;
    this.FuelType = itemIn.FuelType;
    this.BodyType = itemIn.BodyType;
    this.Make = itemIn.Make;

    this.StockLevel = itemIn.StockLevel;
    this.PricePosition = itemIn.PricePosition;
    this.DaysListed = itemIn.DaysListed;
    this.RetailRating = itemIn.RetailRating;
    this.PerformanceRating = itemIn.PerformanceRating;
    this.Profit = itemIn.Profit;
    this.StatsDaysListed = itemIn.StatsDaysListed;
    this.StatsRetailRating = itemIn.StatsRetailRating;
    this.StatsPerformanceRating = itemIn.StatsPerformanceRating;
    this.StatsVsStrategyPrice = itemIn.StatsVsStrategyPrice;

    this.MonthlySalesRate = itemIn.MonthlySalesRate;
    this.SoldPricePosition = itemIn.SoldPricePosition;
    this.SoldDaysListed = itemIn.SoldDaysListed;
    this.SoldProfit = itemIn.SoldProfit;
    this.StockCover = itemIn.StockCover;
    this.AvgStockLevel = itemIn.AvgStockLevel;

  }
  //from stock 
  //dimensions
  RetailerSiteId: number;
  RetailerSiteName: string;
  ModelClean: string;
  PriceBand: string;
  AgeBand: string;
  Drivetrain: string;
  FuelType: string;
  BodyType: string;
  Make:string;

  //values
  StockLevel: number;
  PricePosition: number;
  DaysListed: number;
  RetailRating: number;
  PerformanceRating: number;
  Profit: number;
  StatsDaysListed: StatsDaysListed;
  StatsRetailRating: StatsRetailRating;
  StatsPerformanceRating: StatsPerformanceRating;
  StatsVsStrategyPrice: StatsVsStrategyPrice;

  //from leaving items
  MonthlySalesRate: number;
  SoldPricePosition: number;
  SoldDaysListed: number;
  SoldProfit: number;
  AvgStockLevel: number;
  
  //work out 
  StockCover: number;


}


export interface StockProfileItemDTO {
  //from stock 
  //dimensions
  RetailerSiteId: number;
  RetailerSiteName: string;
  ModelClean: string;
  PriceBand: string;
  AgeBand: string;
  Drivetrain: string;
  FuelType: string;
  BodyType: string;
  Make:string;

  //values
  StockLevel: number;
  PricePosition: number;
  DaysListed: number;
  RetailRating: number;
  PerformanceRating: number;
  Profit: number;
  StatsDaysListed: StatsDaysListed;
  StatsRetailRating: StatsRetailRating;
  StatsPerformanceRating: StatsPerformanceRating;
  StatsVsStrategyPrice: StatsVsStrategyPrice;

  //from leaving items
  MonthlySalesRate: number;
  SoldPricePosition: number;
  SoldDaysListed: number;
  SoldProfit: number;

  //work out 
  StockCover: number;
  AvgStockLevel:number;



}
