//using CPHI.Spark.WebScraper.Helpers;
using log4net;
using Quartz;
using System;
using OfficeOpenXml;
using System.IO;
using System.Linq;
using System.Data;
using System.Collections.Generic;
using System.Drawing;
using OfficeOpenXml.Style;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Repository;
using CPHI.Spark.Reporter.DataAccess;
using Microsoft.EntityFrameworkCore;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;

namespace CPHI.Spark.Reporter.Jobs
{

    //[DisallowConcurrentExecution]
    public partial class ReportUsageStatsJob : IJob
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ReportUsageStatsJob));



        public async Task Execute(IJobExecutionContext context)
        {
            Model.DealerGroupName customer = Model.DealerGroupName.Vindis;
            try
            {
                Logger.Info($@"[{DateTime.UtcNow}]  | Starting to Execute");

                using (var db = new CPHIDbContext())
                {
                    SetDBContextConnectionString(db, customer);
                    CommissionDataAccess dbc = new CommissionDataAccess(db);
                    IEnumerable<UsageReportRecipient> people = await dbc.GetUsersWithLogins(customer);
                    IEnumerable<DailyUsageItem> dailyUsageItems = await dbc.GetDailyUsageItems(customer);

                    foreach (var person in people)
                    {
                        person.DailyUsageItems = dailyUsageItems.Where(x => x.PersonId == person.PersonId);
                    }

                    var peopleToSendReportTo = people.Where(x => x.RoleName != "Limited Access"); //salesmen don't get the usage report

                    //cut down to only those people we have chosen, if we have chosen a limit list
                    if (ConfigService.UseageReportTo.Count > 0)
                    {
                        peopleToSendReportTo = people.Where(x => ConfigService.UseageReportTo.Contains(x.Email));
                    }


                    foreach (var reportPerson in peopleToSendReportTo)
                    {
                        if (reportPerson.SiteNames.Length == 0 || reportPerson.Email == null) { continue; }
                        //if (ConfigService.SendReportsOnlyToMe && reportPerson.Name != "Andrew Tollman") { continue; }

                        if (ConfigService.UseageReportTo.Count == 0)
                        {
                            //we are runnig for everyone, so ensure we skip these 'funny' users
                            if (reportPerson.Email.Contains("gmail")) { continue; }
                            if (reportPerson.Email.Contains("cphi")) { continue; }
                            if (reportPerson.Email.Contains("example")) { continue; }
                            if (reportPerson.Email.Contains("apple.user")) { continue; }
                        }

                        FileInfo fi = new FileInfo(@".\Templates\UserReport.xlsx");
                        using (var p = new ExcelPackage(fi))
                        {
                            ExcelWorksheet reportSheet = p.Workbook.Worksheets["REPORT"];
                            using (ExcelNamedRange firstDataRow = p.Workbook.Names["firstDataRow"])
                            {

                                //aa.  Clear data
                                int lastRow = reportSheet.Dimension.End.Row;
                                int lastCol = reportSheet.Dimension.End.Column;
                                reportSheet.Cells[7, 2, lastRow, lastCol].Value = null;

                                //ab. Set the date headers in the file
                                for (int i = -1; i > -22; i--)
                                {
                                    var dateCell = reportSheet.Cells["AA4"].Offset(0, i + 1);
                                    var dateCellName = reportSheet.Cells["AA5"].Offset(0, i + 1);
                                    dateCell.Value = DateTime.Now.AddDays(i).ToString("dd-MMM");
                                    dateCellName.Value = DateTime.Now.AddDays(i).ToString("ddd");
                                }

                                //b. loop through putting into file
                                int baseCol = firstDataRow.Start.Column;
                                int baseRow = firstDataRow.Start.Row;

                                List<SummaryUsageItem> tempUsageDataPeopleItems = new List<SummaryUsageItem>();

                                //filter down those people who work at the sites that this person has access to
                                List<UsageReportRecipient> thisPersonPeople = people.Where(x => reportPerson.SiteNames.Contains(x.SiteDescription)).OrderByDescending(x => x.DailyUsageItems.Count()).ToList();

                                int rowIndex = 0;
                                foreach (var person in thisPersonPeople)
                                {

                                    //if (person.Name == "Tim Gibson")
                                    //{
                                    //    { }
                                    //}
                                    int daysUsed = person.DailyUsageItems.Count();
                                    string lastUsed = string.Empty;
                                    if (daysUsed > 0)
                                    {
                                        lastUsed = person.DailyUsageItems.OrderByDescending(x => x.ChangeDate).First().ChangeDate.Value.ToString("dd-MMM");
                                    }

                                    //fill out summary columns
                                    reportSheet.Cells["B7"].Offset(rowIndex, 0).Value = person.Name;
                                    reportSheet.Cells["C7"].Offset(rowIndex, 0).Value = person.JobTitle;
                                    reportSheet.Cells["D7"].Offset(rowIndex, 0).Value = person.SiteDescription;
                                    reportSheet.Cells["E7"].Offset(rowIndex, 0).Value = person.SiteNames;
                                    reportSheet.Cells["F7"].Offset(rowIndex, 0).Value = lastUsed;
                                    reportSheet.Cells["AC7"].Offset(rowIndex, 0).Value = daysUsed;

                                    //fill out detail columns
                                    int daysUsedCount = 0;
                                    for (int dayIndex = -1; dayIndex > -22; dayIndex--)
                                    {
                                        DateTime day = DateTime.Today.AddDays(dayIndex);
                                        bool isUsed = person.DailyUsageItems.FirstOrDefault(x => x.ChangeDate == day) != null;

                                        ExcelRangeBase currCell = reportSheet.Cells["AB7"].Offset(rowIndex, dayIndex);
                                        if (isUsed)
                                        {
                                            setCellToYes(currCell);
                                            daysUsedCount++;
                                        }
                                        else
                                        {
                                            setCellToNo(currCell);
                                        }
                                    }

                                    rowIndex++;
                                }



                            }

                            //Local Path
                            string path = $@"C:\Output\UsageAsAt{DateTime.Now.ToString("yy-MM-dd")}.xlsx";

                            FileInfo file = new FileInfo(path);
                            p.SaveAs(file);
                            List<string> tos = new List<string>
                            {
                                reportPerson.Email
                            };
                            List<string> ccs = ConfigService.UseageReportCc.ToList();
                            await EmailerService.SendMail(customer, $"Spark Usage Report for {reportPerson.Name}", "Please see attached the usage statistics for Spark for the last 3 weeks.", path, Logger, tos, ccs);
                            File.Delete(path);
                        }

                    }
                }
            }
            catch (Exception e)
            {
                Logger.Error(e);
            }
        }

        private void setCellToYes(ExcelRangeBase currCell)
        {
            currCell.Value = "Y";
            currCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#99FF99");
            currCell.Style.Fill.BackgroundColor.SetColor(colFromHex);
        }
        private void setCellToNo(ExcelRangeBase currCell)
        {
            currCell.Value = "N";
            currCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#BFBFBF");
            currCell.Style.Fill.BackgroundColor.SetColor(colFromHex);
        }



        private void SetDBContextConnectionString(CPHIDbContext db, Model.DealerGroupName customer)
        {
            db.Database.GetDbConnection().ConnectionString = ConfigService.GetConnectionString(customer);
        }


    }

 






}

