﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.IE;
using OpenQA.Selenium.Interactions;
using OpenQA.Selenium.Support.UI;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using System.Data;
using Dapper;
using CPHI.Spark.Model;
using System.Threading;
using CPHI.Repository;
using SeleniumExtras.WaitHelpers;
using System.Globalization;
using System.Text;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.WebScraper.Services;
using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.Spark.WebScraper.Jobs
{
   [DisallowConcurrentExecution]
   public class BrindleyPinewoodUpdatePriceJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(BrindleyPinewoodUpdatePriceJob));
      private static InternetExplorerDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
      private static bool stillHaveItemsToPost = true;
      private static CommonMethods _commonMethods;


      private static string mainWindowId = null;

      private static LogMessage logMessage;

      public void Execute() { }

      public async Task Execute(IJobExecutionContext context)
      {
         try
         {
            int attemptCount = 0;

            logMessage = new LogMessage();
            logMessage.SourceDate = DateTime.UtcNow;

            stillHaveItemsToPost = true;

            var emailerService = new EmailerService();

            //New!  Firstly check if we have any repeated price changes from yesterday that are within the small threshold currrently set for the retailer site usually + or - £50
            //if this happens, it suggests something went wrong with yesterday's keying so we need to know about it
            var repeatChanges = await CreatePinewoodDayOnDaySamePriceChangeList();

            if (repeatChanges.Count > 0)
            {
               string repeatBody = BuildRepeatedChangeEmailBody(repeatChanges);
               await emailerService.SendMail("😔 Brindley Pinewood Job - Repeat Price Changes Detected", repeatBody);
            }


            while (attemptCount < 5 && stillHaveItemsToPost)
            {
               logger.Info($"");
               logger.Info($"------------------------------------------------");
               logger.Info($"Attempt #{attemptCount}");
               try
               {
                  await CarryOutFullEndToEndAttempt();
               }
               catch (Exception ex)
               {
                  //should have already logged the issue
                  { }
               }

               //finished.   Now sleep for some time then go to next attempt, in case there are more items to post.  e.g. perhaps we crashed out of the attempt
               System.Threading.Thread.Sleep(4000);
               attemptCount++;
            }

            //we have finished naturally, do a report
            List<PinewoodPriceChange> latestPriceChangeStatus = await CreatePinewoodPriceChangeList();

            //build up report email
            string emailBody = BuildEmailBody(latestPriceChangeStatus);

            await emailerService.SendMail("Brindley Pinewood Job Summary", emailBody);

            logger.Info("Finished naturally");
         }
         catch (Exception ex)
         {
            logger.Info(ex);
            logMessage.ErrorCount = 1;
            logMessage.FailNotes += ex.StackTrace.ToString();
            EmailerService eService = new EmailerService();
            await eService.SendMail("❌ FAILURE BrindleyPinewoodUpdatePriceJob ", $"{ex.StackTrace}");
         }
         finally
         {

            if (stillHaveItemsToPost)
            {
               EmailerService eService = new EmailerService();
               await eService.SendMail("❌ FAILURE BrindleyPinewoodUpdatePriceJob ", $"It still have items to post.");
            }

            DealerGroupName dealerGroup = DealerGroupName.BrindleyGroup;
            string connString = ConfigService.GetConnectionString(dealerGroup);

            using (var db = new CPHIDbContext(connString))
            {
               logMessage.FinishDate = DateTime.UtcNow;
               logMessage.DealerGroup_Id = (int)dealerGroup;
               logMessage.Job = "PinewoodPriceUpdates";
               db.LogMessages.Add(logMessage);
               await db.SaveChangesAsync();
            }
         }

      }

      private string BuildEmailBody(List<PinewoodPriceChange> items)
      {
         var cultureInfo = new CultureInfo("en-GB");
         var sb = new StringBuilder();

         sb.Append("<html>");
         sb.Append("<body>");
         sb.Append("<h2>Status Report</h2>");
         sb.Append("<table>");
         sb.Append("" +
             "<tr>" +
             "<th>PriceChangeId</th>" +
             "<th>IsAuto</th>" +
             "<th>SiteName</th>" +
             "<th>StockNumber</th>" +
             "<th>NewPrice</th>" +
             "<th>SavedOk</th>" +
             "<th>SaveError</th>" +
             "</tr>");

         foreach (var item in items)
         {
            sb.Append($"<tr>" +
                $"<td>{item.PriceChangeId}</td>" +
                $"<td>{(item.IsAutoPriceChange ? "true" : "false")}</td>" +
                $"<td>{item.SiteName}</td>" +
                $"<td>{item.StockNumber}</td>" +
                $"<td>{item.NewPrice.ToString("C0", cultureInfo)}</td>" +
                $"<td>{(item.StillNeedsKeying ? "false" : "true")}</td>" +
                $"<td>{item.SaveError}</td>" +
                $"</tr>");
         }

         sb.Append("</table>");
         sb.Append("</body>");
         sb.Append("</html>");

         return sb.ToString();
      }

      private string BuildRepeatedChangeEmailBody(List<PinewoodRepeatedPriceChange> items)
      {
         var cultureInfo = new CultureInfo("en-GB");
         var sb = new StringBuilder();

         sb.Append("<html>");
         sb.Append("<body>");
         sb.Append("<h2>Repeat Price Change Report</h2>");
         sb.Append("<table>");
         sb.Append("" +
             "<tr>" +
             "<th>StockNumberFull</th>" +
             "<th>TodayNowPrice</th>" +
             "<th>YesterdayNowPrice</th>" +
             "<th>DailyDifference</th>" +
             "<th>MinimumAutoPriceDecrease</th>" +
             "<th>MinimumAutoPriceIncrease</th>" +
             "</tr>");

         foreach (var item in items)
         {
            sb.Append($"<tr>" +
                $"<td>{item.StockNumberFull}</td>" +
                $"<td>{item.TodayNowPrice}</td>" +
                $"<td>{item.YesterdayNowPrice}</td>" +
                $"<td>{item.DailyDifference}</td>" +
                $"<td>{item.MinimumAutoPriceDecrease}</td>" +
                $"<td>{item.MinimumAutoPriceIncrease}</td>" +
                $"</tr>");
         }

         sb.Append("</table>");
         sb.Append("</body>");
         sb.Append("</html>");

         return sb.ToString();
      }



      private static async Task CarryOutFullEndToEndAttempt()
      {

         try
         {
            logger.Info($"Starting Brindley Pinewood Scrape job");

            // 1. Get data from DB
            List<PinewoodPriceChange> allPriceChanges = await CreatePinewoodPriceChangeList();
            var toKey = allPriceChanges.Where(x => x.StillNeedsKeying && x.StockNumber != "Unknown").ToList();

            if (toKey.Count == 0)
            {
               stillHaveItemsToPost = false;
               // logger.Info($"No price changes. Returning..."); removing to reduce log spam
               return;
            }

            logger.Info($"{toKey.Count} priceChange(s) still to go");
            var groupedBySite = toKey.GroupBy(p => p.SiteName);
            Dictionary<string, string> siteLookupDict = CreateSiteLookupDict();

            // 2. Setting up the environment
            InternetExplorerOptions ieOptions = ScraperMethodsService.SetIEOptions();
            //ieOptions.IntroduceInstabilityByIgnoringProtectedModeSettings = true; //already set in SetIEOptions
            ieOptions.EnablePersistentHover = false;
            ScraperMethodsService.KillOldEdgeInstances();
            _driver = new InternetExplorerDriver(ieOptions);
            _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(5);
            _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(20);
            _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(20);
            _commonMethods = new CommonMethods(_driver, logger);


            try
            {

               Login();  //ok
               logger.Info("Logged in");

               _commonMethods.Sleep(1000);
               mainWindowId = _driver.CurrentWindowHandle;

               //Go to stock search
               WaitAndClickButtonByText("Vehicles");
               WaitAndClickButtonByText("Reporting");
               WaitAndClickButtonByText("Stock Cards");

               AutoTraderStockClient atStockClient = new AutoTraderStockClient(
                            HttpClientFactoryService.HttpClientFactory,
                            ConfigService.AutoPriceConnectionString,
                            ConfigService.AutotraderApiSecret,
                            ConfigService.AutotraderBaseURL
                            );

               AutoTraderApiTokenClient atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory,ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret ,ConfigService.AutotraderBaseURL );

               var token = await atTokenClient.GetToken();

               /// --------------------------
               ///SITE LOOP
               /// --------------------------
               int priceChangeCount = 0;
               foreach (var group in groupedBySite)
               {
                  logger.Info($"------------------------------------------------------------");
                  logger.Info($"Starting site: {group.Key}");
                  logger.Info($"Site has: {group.Count()} item(s)");

                  WaitForNumberOfWindowsToBeNew(2, 10);

                  try
                  {

                     string siteName = siteLookupDict[group.First().SiteName];

                     if (siteName == "Brindley Gold Star")
                     {
                        continue;
                     }

                     ChooseSite(siteName);

                     /// -----------------------------
                     /// PRICE CHANGE LOOP
                     /// -----------------------------
                     foreach (PinewoodPriceChange change in group)
                     {
                        priceChangeCount++;
                        logger.Info($"");
                        logger.Info($"----------Price change {priceChangeCount} of {toKey.Count()}--------------------------------------------------");
                        logger.Info($"\t Stocknumber: {change.StockNumber}, NewPrice: {change.NewPrice}");

                        try
                        {

                           SearchForStockNumber(change);

                           DetectAndThrowIfBadStockStatus();
                           SwitchToMainWindow(new List<int>() { 1, 0 });
                           ClickAmendButton("Button_Amend");

                           string amendWindowHandle = await UpdatePriceAndSave(change, token,atStockClient, atTokenClient);
                           bool weHadFirstPopup = WaitAndDismissConfirmationPopUpIfHappens(amendWindowHandle);
                           if (weHadFirstPopup)
                           {
                              WaitAndDismissConfirmationPopUpIfHappens(amendWindowHandle);
                           }

                           GoBackToStockSearch();
                           change.SavedOk = true;
                        }
                        catch (NotSupportedException ex)
                        {
                           //we throw these for things that are at a bad status, don't want to keep processing this
                           logger.Info($"Could not process price change {change.StockNumber} due to stock status");
                           change.SavedOk = true;
                           change.SaveError = ex.Message;

                           //now must return the screen to the stock search
                           GoBackToStockSearch();
                        }
                        catch (Exception ex)
                        {
                           logger.Error(ex);
                           change.SavedOk = false;
                           change.SaveError = ex.Message;

                           CloseNonMainWindow();

                           //now must return the screen to the stock search
                           GoBackToStockSearch();
                        }

                        try
                        {
                           await SaveChangeResult(change);  //save as we go as seems flaky
                           logger.Info($"Saved price change for {change.StockNumber} OK.");
                        }

                        catch (Exception ex)
                        {
                           logger.Error($"Failed to save price change for {change.StockNumber} {ex.Message}");
                        }

                     }


                     logger.Info($"Finished site {group.First().SiteName}");

                     //Bring up site selection menu for next site to use
                     SwitchToMainWindow(new List<int>() { 1 });
                     _commonMethods.ClickButtonById("cellDescription"); //this brings up the site selection window

                  }
                  catch (Exception ex)
                  {
                     continue;
                  }
               }

            }
            catch (Exception ex)
            {
               logger.Info("An error occurred: " + ex.Message);


            }
            finally
            {
               CloseNonMainWindow();
               SwitchToMainWindow(new List<int>() { 1 });
               //string xPath = $"//button[text()[normalize-space(.)='Log Off']]";
               //var element = _commonMethods.NavigateAndReturnElementThroughWindowsAndFrames(By.XPath(xPath));
               //_commonMethods.ClickElement(element);
               WaitAndClickButtonByText("Log Off");
               _driver.Quit();
               _driver.Dispose();
            }

         }

         catch (Exception e)
         {
            logger.Error($"Problem {e.ToString()}");
            throw new Exception("General fail");
         }
      }

      private static void ChooseSite(string siteName)
      {
         string siteSelectionWindowHandle = _driver.WindowHandles.Where(x => x != mainWindowId).FirstOrDefault();

         if (siteSelectionWindowHandle == null)
         {
            logger.Info("Could not find site - unable to get main window handle.");
            throw new Exception("Could not find site - unable to get main window handle.");
         }

         SwitchToWindowHandle(siteSelectionWindowHandle, new List<int>() { 1 });
         string companyNameInputXPath = "//input[@name='form1$txtCompany']";
         var inputBox = _commonMethods.WaitAndFindElement(By.XPath(companyNameInputXPath));

         if (inputBox == null)
         {
            throw new Exception("Could not find company name input box");
         }

         _commonMethods.ClickElement(inputBox);

         inputBox.SendKeys(siteName);
         string theSiteListItemXPath = $"//li[contains(@class, 'buttonList_container') and contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{siteName.ToLower()}')]";
         IWebElement siteListItem = _commonMethods.WaitAndFindElement(By.XPath(theSiteListItemXPath));

         if (siteListItem == null)
         {
            logger.Info("Could not find site.");
            throw new Exception("Could not find site");
         }
         // Count the number of radio buttons within the list item
         int buttonsCount = siteListItem.FindElements(By.XPath(".//input[@type='radio']")).Count();
         _commonMethods.ClickElement(inputBox);
         _commonMethods.Sleep(100);
         Actions actions = new Actions(_driver);
         actions.SendKeys(Keys.Down).Perform();
         for (int i = 0; i < buttonsCount; i++)
         {
            _commonMethods.Sleep(100);
            actions.SendKeys(Keys.Right).Perform();
         }
         _commonMethods.Sleep(500);
         actions.SendKeys(Keys.Enter).Perform();
      }

      private static void GoBackToStockSearch()
      {
         SwitchToMainWindow(new List<int>() { 1 });
         string stockCardsXPath = $"//button[text()[normalize-space(.)='Stock Cards']]";
         bool ok = WaitAndClickElement(By.XPath(stockCardsXPath), stockCardsXPath);
         if (!ok)
         {
            { }
         }
      }

      private static void CloseNonMainWindow()
      {
         WaitForNumberOfWindowsToBeNew(2, 3);
         if (_driver.WindowHandles.Count > 1)
         {
            string nonMainWindow = _driver.WindowHandles.Where(x => x != mainWindowId).FirstOrDefault();
            SwitchToWindowHandle(nonMainWindow);
            try
            {
               _driver.Close();
            }
            catch (Exception ex1)
            {
               throw new Exception("Failed trying to clean up second window");
            }
         }
      }

      private static void DetectAndThrowIfBadStockStatus()
      {
         logger.Info("Detecting Stock status...");

         SwitchToMainWindow(new List<int>() { 1, 0, 0 });

         string statusXPath = "/html/body/form[@name='VehicleForm']//span[@id='Status']";
         var status = _commonMethods.WaitAndFindElement(By.XPath(statusXPath), 3);

         if (status == null)
         {
            { }
         }

         string statusText = status.Text;

         if (statusText == "In Stock (Customer Order)")
         {
            logger.Info("Vehicle on customer order, do not update");
            throw new NotSupportedException("Vehicle on customer order, do not update");
         }
         else if (statusText == "Invoiced")
         {
            logger.Info("Vehicle has been invoiced, do not update");
            throw new NotSupportedException("Vehicle has been invoiced, do not update");
         }
         else if (statusText == "Finalised")
         {
            logger.Info("Vehicle has been finalised, do not update");
            throw new NotSupportedException("Vehicle has been finalised, do not update");
         }
      }

      private static void GotoStockSearchScreen()
      {
         //string stockNumberInputBoxXPath = "//table[@id='Table1']//form[@id='Form1']//input[@name='StockNumber']";
         SwitchToMainWindow();
         bool couldClickStockCards = WaitAndClickButtonByText("Stock Cards");
         //_commonMethods.SwitchWindowAndFrame(0, 1, stockNumberInputBoxXPath);
      }

      private static async Task SaveChangeResult(PinewoodPriceChange change)
      {
         using (var db = new CPHIDbContext(ConfigService.GetConnectionString(DealerGroupName.BrindleyGroup)))
         {
            if (change.IsAutoPriceChange)
            {
               var dbItem = db.PriceChangeAutoItems.FirstOrDefault(x => x.Id == change.PriceChangeId);
               if (dbItem == null)
               {
                  throw new Exception("Db item not found, are you running in debug with dummy items?");
               }
               if (change.SavedOk)
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.DateConfirmed = DateTime.UtcNow;
               }
               else
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.SaveResult = change.SaveError;
               }
            }
            else
            {
               var dbItem = db.PriceChangeManualItems.FirstOrDefault(x => x.Id == change.PriceChangeId);
               if (dbItem == null)
               {
                  throw new Exception("Db item not found, are you running in debug with dummy items?");
               }
               if (change.SavedOk)
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.DateConfirmed = DateTime.UtcNow;
               }
               else
               {
                  dbItem.DateSent = DateTime.UtcNow;
                  dbItem.SaveResult = change.SaveError;
               }
            }

            await db.SaveChangesAsync();
         }
      }

      private static void SwitchToMainWindow(List<int> frames = null)
      {
         logger.Info("Switching to main window..");
         _driver.SwitchTo().Window(mainWindowId);

         if (frames != null)
         {
            foreach (var frameIndex in frames)
            {
               _commonMethods.SwitchToFrameNew(frameIndex);
            }
         }
      }

      private static void SwitchToWindowHandle(string windowHandle, List<int> frames = null)
      {
         logger.Info($"Switching to named window {windowHandle}");
         _driver.SwitchTo().Window(windowHandle);

         if (frames != null)
         {
            foreach (var frameIndex in frames)
            {
               _commonMethods.SwitchToFrameNew(frameIndex);
            }
         }
      }





      private static async Task<List<PinewoodPriceChange>> CreatePinewoodPriceChangeList()
      {
         using (var dapper = new Dapperr())
         {
            var paramList = new DynamicParameters();

            paramList.Add("DealerGroupId", (int)DealerGroupName.BrindleyGroup);

            return (await dapper.GetAllAsync<PinewoodPriceChange>(DealerGroupName.BrindleyGroup, "[autoprice].[GET_PriceChangesToKeyIntoDMS]", paramList, CommandType.StoredProcedure)).ToList();
         }
      }


      private static async Task<List<PinewoodRepeatedPriceChange>> CreatePinewoodDayOnDaySamePriceChangeList()
      {
         using (var dapper = new Dapperr())
         {
            var paramList = new DynamicParameters();

            paramList.Add("DealerGroupId", (int)DealerGroupName.BrindleyGroup);

            return (await dapper.GetAllAsync<PinewoodRepeatedPriceChange>(DealerGroupName.BrindleyGroup, "[autoprice].[GET_CheckChangesToKeyIntoDMSNotRepeated]", paramList, CommandType.StoredProcedure)).ToList();
         }
      }


      private static void SearchForStockNumber(PinewoodPriceChange priceChange)
      {
         logger.Info("Searching for stock number...");
         SwitchToMainWindow(new List<int>() { 1, 0 });
         string stockNumberXpath = "//table[@id='Table1']//form[@id='Form1']//input[@name='StockNumber']";
         IWebElement stockNumberInputBox = _commonMethods.WaitAndFindElement(By.XPath(stockNumberXpath), 3);//
         if (stockNumberInputBox == null)
         {
            { }
            //problem, perhaps we are not back at the start as we thought we were
            GoBackToStockSearch();
            logger.Info("Searching again to find stock number...");
            SwitchToMainWindow(new List<int>() { 1, 0 });
            stockNumberInputBox = _commonMethods.WaitAndFindElement(By.XPath(stockNumberXpath), 3);//

            if (stockNumberInputBox == null)
            {
               logger.Info("Could not find stock number input box");
               throw new Exception("Could not find stock number input box");
            }
         }

         _commonMethods.ClickElement(stockNumberInputBox);
         stockNumberInputBox.Clear();
         SendKeysToElementAsIndividualLetters(stockNumberInputBox, new List<string>() { priceChange.StockNumber });
         WaitAndClickButtonByText("Search");



         //string listOfResultsXPath = "//form[@name='ScrollList']//table[@id='SLTable' and @class='ScrollList2004Table']";
         SwitchToMainWindow(new List<int> { 1, 0, 0 });
         var matchingItemXPath = $"//td[div[contains(text(), '{priceChange.StockNumber}')]]";
         var matchingItem = _commonMethods.WaitAndFindElement(By.XPath(matchingItemXPath));

         if (matchingItem == null)
         {
            logger.Info("Could not find match when searching for stocknumber");
            throw new Exception("Could not find match when searching for stocknumber");
         }

         DoubleClick(matchingItem);
      }

      private static void ClickAmendButton()
      {
         // Click Amend button
         //string amendButtonXPath = "//form[@name='StockCardTemplate']//div[@class='buttonHolder__actions'] //*[@id='Button_Amend']";
         SwitchToMainWindow(new List<int>() { 1, 0 });
         ClickAmendButton("Button_Amend");
      }



      private static async Task<string> UpdatePriceAndSave(PinewoodPriceChange priceChange,TokenResponse token, AutoTraderStockClient atStockClient, AutoTraderApiTokenClient atTokenClient)
      {
         logger.Info("Updating price and save...");

         //go to new amend screen
         WaitForNumberOfWindowsToBeNew(2, 5);
         string amendWindowHandle = _driver.WindowHandles.Where(x => x != mainWindowId).First();
         SwitchToWindowHandle(amendWindowHandle, new List<int>() { 1 });
         IWebElement sellingPriceBoxCheck = _commonMethods.WaitAndFindElement(By.Id("txtSellingPriceTotal_Display"), 10);// _driver.FindElement();

         IWebElement sellingPriceBox = _commonMethods.WaitAndFindElement(By.Id("txtSellingPriceTotal_Display"));
         IWebElement internetPriceBox = _commonMethods.WaitAndFindElement(By.Id("txtInternetPrice_Display")); //_driver.FindElement(By.Id("txtInternetPrice_Display"));
         bool isAlreadyOk = CheckIfPriceIsAlreadyOk(priceChange, sellingPriceBox, internetPriceBox);
         bool isInternetPriceIsZero = CheckIfInternetPriceIsZero(internetPriceBox);

         if (!isAlreadyOk)
         {
            //update prices and check
            UpdateSellingPrice(priceChange, sellingPriceBox);
            if (!isInternetPriceIsZero)
            {
               UpdateInternetPrice(priceChange, sellingPriceBox, internetPriceBox);
            }
            try
            {

               DoubleCheckIsRight(priceChange, sellingPriceBox, internetPriceBox, isInternetPriceIsZero);
               //Save
               _commonMethods.ClickButtonById("btnSave");
               //still here, must have been ok, now update Autotrader.   COMMENTED OUT FOR NOW DO NOT DELETE
               //await UpdateAutoTraderForPriceChange(priceChange.WebsiteStockIdentifier, priceChange.NewPrice, priceChange.AdvertiserId, ConfigService.AutotraderBaseURL,  token, atStockClient,atTokenClient );
               //logger.Info($"Updated price on AT and saved for stock number {priceChange.StockNumber}, new price: {priceChange.NewPrice}");
            }
            catch (Exception ex)
            {
               throw;
            }

         }
         else
         {
            _driver.Close();
         }


         return amendWindowHandle;
      }


      //DO NOT DELETE
      private static async Task UpdateAutoTraderForPriceChange(string stockId, int newPrice, int advertiserId, string atBaseUrl, TokenResponse atToken, AutoTraderStockClient atStockClient, AutoTraderApiTokenClient tokenClient)
      {
         // This method updates the price on AutoTrader using the AutoTraderStockClient
         if (atStockClient != null)
         {
            try
            {
               // Create the UpdatePriceParams object required by the AutoTraderStockClient
               var updateParams = new UpdatePriceParams
               {
                  WebsiteStockIdentifier = stockId,
                  NewPrice = newPrice,
                  AdvertiserId = advertiserId
               };


               // Call the API to update the price
               atToken = await tokenClient.CheckExpiryAndRegenerate(atToken);
               string result = await atStockClient.UpdatePrice(updateParams, atToken.AccessToken, atBaseUrl);
               logger.Info($"AutoTrader price update result: {result}");
            }
            catch (Exception ex)
            {
               logger.Error($"Failed to update AutoTrader price: {ex.Message}");
            }
         }
         else
         {
            logger.Warn("AutoTrader client not initialized, skipping price update");
         }
      }

      private static IWebElement UpdateSellingPrice(PinewoodPriceChange priceChange, IWebElement sellingPriceBox)
      {

         _commonMethods.ClickElement(sellingPriceBox);
         sellingPriceBox.Clear();
         _commonMethods.ClickElement(sellingPriceBox);
         sellingPriceBox.SendKeys(priceChange.NewPrice.ToString());
         _commonMethods.Sleep(600);
         sellingPriceBox.SendKeys(Keys.Tab);
         return sellingPriceBox;
      }

      private static IWebElement UpdateInternetPrice(PinewoodPriceChange priceChange, IWebElement sellingPriceBox, IWebElement internetPriceBox)
      {

         _commonMethods.ClickElement(internetPriceBox);
         internetPriceBox.Clear();
         _commonMethods.ClickElement(internetPriceBox);
         internetPriceBox.SendKeys(priceChange.NewPrice.ToString());
         _commonMethods.ClickElement(sellingPriceBox);
         _commonMethods.Sleep(600);
         internetPriceBox.SendKeys(Keys.Tab);
         return internetPriceBox;
      }

      private static void DoubleCheckIsRight(PinewoodPriceChange priceChange, IWebElement sellingPriceBox, IWebElement internetPriceBox, bool skipInternetPriceCheck)
      {
         _commonMethods.Sleep(500);
         var nowSellingPriceValue = sellingPriceBox.GetAttribute("value");
         if (!AreAMatch(nowSellingPriceValue, priceChange.NewPrice))
         {
            throw new Exception($"Failed, tried to set price to {priceChange.NewPrice.ToString()} but it is showing now as {nowSellingPriceValue} for item {priceChange.StockNumber}");
         }

         if (!skipInternetPriceCheck)
         {
            var nowInternetPriceValue = internetPriceBox.GetAttribute("value");
            if (!AreAMatch(nowInternetPriceValue, priceChange.NewPrice))
            {
               throw new Exception($"Failed, tried to set price to {priceChange.NewPrice.ToString()} but it is showing now as {nowInternetPriceValue} for item {priceChange.StockNumber}");
            }
         }
      }

      private static bool CheckIfPriceIsAlreadyOk(PinewoodPriceChange priceChange, IWebElement sellingPriceBox, IWebElement internetPriceBox)
      {
         _commonMethods.Sleep(500);
         var nowSellingPriceValue = sellingPriceBox.GetAttribute("value");
         if (!AreAMatch(nowSellingPriceValue, priceChange.NewPrice))
         {
            return false;
         }
         var nowInternetPriceValue = internetPriceBox.GetAttribute("value");
         if (!AreAMatch(nowInternetPriceValue, priceChange.NewPrice))
         {
            return false;
         }

         return true;
      }

      private static bool CheckIfInternetPriceIsZero(IWebElement internetPriceBox)
      {
         _commonMethods.Sleep(500);

         var nowInternetPriceValue = internetPriceBox.GetAttribute("value");
         if (AreAMatch(nowInternetPriceValue, 0))
         {
            return true;
         }

         return false;
      }


      private static bool AreAMatch(string stringNumber, int intNumber)
      {

         // Remove commas and parse to a decimal
         if (decimal.TryParse(stringNumber.Replace(",", ""), out decimal parsedNumber))
         {
            // Convert the decimal to an integer (this assumes the decimal part is .00)
            int parsedInt = (int)parsedNumber;

            // Compare the integers
            return parsedInt == intNumber;
         }
         else
         {
            return false;
         }
      }





      private static bool WaitAndDismissConfirmationPopUpIfHappens(string existingSecondWindow)
      {
         logger.Info("Awaiting popup...");
         bool isPopUp = WaitForNumberOfWindowsToBeNew(3, 2);

         if (isPopUp)
         {
            logger.Info("Pop up found");

            //_commonMethods.SwitchWindowAndFrame(2, 1, btnYesXPath);

            string popupHandle = _driver.WindowHandles.Where(x => x != mainWindowId && x != existingSecondWindow).First();
            SwitchToWindowHandle(popupHandle, new List<int>() { 1 });
            string btnYesXPath = $"//button[@id='btnYes']";
            IWebElement yesButton = _commonMethods.WaitAndFindElement(By.XPath(btnYesXPath), 3);

            bool clickedOk = WaitAndClickElement(By.XPath(btnYesXPath), btnYesXPath);

            return true;

         }
         else
         {
            logger.Info("No popup found");
            return false;
         }

      }



      public static bool WaitForNumberOfWindowsToBeNew(int expectedNumberOfWindows, int timeoutInSeconds)
      {
         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutInSeconds));
            wait.PollingInterval = TimeSpan.FromMilliseconds(100); // Poll more frequently

            // Small delay before starting to check
            Thread.Sleep(200);

            return wait.Until(d => d.WindowHandles.Count == expectedNumberOfWindows);
         }
         catch (WebDriverTimeoutException)
         {
            Console.WriteLine("Timeout waiting for the number of windows to be: " + expectedNumberOfWindows);
            return false;
         }
      }



      public static void SendKeysToElementAsIndividualLetters(IWebElement element, List<string> keys)
      {
         try
         {
            foreach (var key in keys)
            {
               element.SendKeys(key);
            }
         }
         catch (Exception ex)
         {
            { }
         }
      }


      private static void SwitchToResultsAndChooseVehicle(PinewoodPriceChange priceChange)
      {

      }



      // Function to create and initialize the dictionary
      // Site name and then down presses
      private static Dictionary<string, string> CreateSiteLookupDict()
      {
         return new Dictionary<string, string>
            {
                { "Brindley Honda Cannock", "Brindley Honda Cannock" },
                { "Brindley Honda West Bromwich", "Brindley Honda West Bromwich" },
                { "Brindley Hyundai West Bromwich", "Brindley Hyundai West Bromwich"  },
                { "Brindley Honda Wolverhampton", "Brindley Honda Wolverhampton" },
                { "Brindley Mazda Wolverhampton", "Brindley Mazda Wolverhampton" },
                { "Brindley Volvo Wolverhampton","Brindley Volvo Wolverhampton"  },
                { "Brindley Kia Wolverhampton", "Brindley Kia Penn Road" },
                { "Brindley Hyundai Wolverhampton", "Brindley Hyundai Penn Road" },
                { "Goldstar Used Car Centre", "Brindley Omoda,Jaecoo,Maxus" },
                { "Brindley Nissan Cannock", "Brindley Nissan" },
                { "Brindley Kia West Bromwich", "Brindley Kia West Bromwich" },
                { "Central Car Clearance", "Central Car Clearance" },
                { "Brindley Mg Wolverhampton", "Brindley MG Wolverhampton" },
                { "Brindley Hyundai Cannock", "Brindley Hyundai Cannock" },

            };
      }
      private static List<PinewoodPriceChange> CreateDummyPinewoodPriceChangeList()
      {
         List<PinewoodPriceChange> results = new List<PinewoodPriceChange>()
            {

                new PinewoodPriceChange(){StockNumber = "UD5168",SiteName = "Brindley Honda Cannock",NewPrice = 4169,},
new PinewoodPriceChange(){StockNumber = "UD5178",SiteName = "Brindley Honda Cannock",NewPrice = 24367,},
new PinewoodPriceChange(){StockNumber = "UD5177",SiteName = "Brindley Honda Cannock",NewPrice = 28603,},
new PinewoodPriceChange(){StockNumber = "UD5175",SiteName = "Brindley Honda Cannock",NewPrice = 23514,},
new PinewoodPriceChange(){StockNumber = "UG832",SiteName = "Brindley Mg Cannock",NewPrice = 19032,},
new PinewoodPriceChange(){StockNumber = "UG776",SiteName = "Brindley Mg Cannock",NewPrice = 17891,},
new PinewoodPriceChange(){StockNumber = "UG792",SiteName = "Brindley Mg Cannock",NewPrice = 10405,},
new PinewoodPriceChange(){StockNumber = "UN5253",SiteName = "Brindley Nissan Cannock",NewPrice = 6391,},
new PinewoodPriceChange(){StockNumber = "UN5233",SiteName = "Brindley Nissan Cannock",NewPrice = 10464,},
new PinewoodPriceChange(){StockNumber = "UV4961",SiteName = "Goldstar Used Car Centre",NewPrice = 20343,},
new PinewoodPriceChange(){StockNumber = "UV4953",SiteName = "Goldstar Used Car Centre",NewPrice = 25287,},
new PinewoodPriceChange(){StockNumber = "CC4924",SiteName = "Central Car Clearance",NewPrice = 13033,},
new PinewoodPriceChange(){StockNumber = "CC4920",SiteName = "Central Car Clearance",NewPrice = 6263,},
new PinewoodPriceChange(){StockNumber = "CC4914",SiteName = "Central Car Clearance",NewPrice = 6984,},


            };

         return results;
      }




      private static void Login()
      {

         try
         {
            _driver.Url = "https://brindley.pinewooddms.com/";

            _commonMethods.Sleep(5000);
            string logonXPath = "html/body/form[@class='loginPage']//input[@name='txtLogonName']";
            _commonMethods.SwitchWindowAndFrame(0, 1, logonXPath, null, null, true);
            var logonEl = _commonMethods.WaitAndFindElement(By.XPath(logonXPath));

            //Maybe just need this next bit if running locally
            //logonEl.Clear();
            //logonEl.Click();
            //logonEl.SendKeys("<EMAIL>");

            // Password should autopopulate once email added
            _commonMethods.Sleep(300);  //unsure if we really need this

            // Send 'Enter' key login (can't seem to get button press working)
            Actions actions = new Actions(_driver);
            actions.SendKeys(Keys.Enter)
                   .Build()
                   .Perform();
         }
         catch (Exception e)
         {
            Console.Error.WriteLine($"Failed to login.");
         }

      }


      private static void DoubleClick(IWebElement element)
      {
         try
         {
            Actions actions = new Actions(_driver);
            actions.DoubleClick(element).Perform();
         }
         catch (Exception ex)
         {
            // Fallback: Use JavaScript to simulate double click
            try
            {
               IJavaScriptExecutor jsExecutor = (IJavaScriptExecutor)_driver;
               jsExecutor.ExecuteScript("arguments[0].dispatchEvent(new MouseEvent('dblclick', {bubbles: true, cancelable: true, view: window}));", element);
            }
            catch (Exception jsEx)
            {
               logger.Error($"Both Actions double-click and JavaScript double-click failed: {ex.Message}, {jsEx.Message}");
               throw;
            }
         }
      }



      private static bool WaitAndClickButtonByText(string buttonText)
      {
         string xPath = $"//button[text()[normalize-space(.)='{buttonText}']]";
         return WaitAndClickElement(By.XPath(xPath), xPath);
      }

      private static bool WaitAndClickElement(By by, string route)
      {
         try
         {
            // Create a WebDriverWait instance with a 10-second timeout
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            // Wait until the element is present
            IWebElement element = wait.Until(ExpectedConditions.ElementExists(by));

            _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(1);
            IWebElement button = _driver.FindElement(by);

            if (button != null)
            {
               logger.Info($"Clicked button with xPath {route}.");
               IJavaScriptExecutor ex = (IJavaScriptExecutor)_driver;
               ex.ExecuteScript("arguments[0].click();", button);
               return true;
            }
            else
            {
               logger.Info($" {route}  not found.");
               return false;
            }
         }
         catch (Exception ex)
         {
            { }
            logger.Info($"Failed to find button {route}");
            return false;
         }
         finally
         {
            _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(30);
         }
      }

      private static void ClickAmendButton(string id)
      {
         //logger.Info($"Attempting to click button with: {id}");

         _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(2);

         try
         {
            // Use XPath to directly find the element with the specified ID
            IWebElement button = _driver.FindElement(By.XPath($"//*[@id='{id}']"));

            if (button != null)
            {
               var handles = _driver.WindowHandles;
               IJavaScriptExecutor jsExecutor = (IJavaScriptExecutor)_driver;
               jsExecutor.ExecuteScript("arguments[0].click();", button);
            }
         }
         catch (Exception ex)
         {
         }
         finally
         {
            _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(30);
         }

      }









   }



}

