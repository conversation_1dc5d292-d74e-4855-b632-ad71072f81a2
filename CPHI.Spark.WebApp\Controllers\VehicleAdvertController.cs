﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing.Taxonomy;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.WebApp.Service.AutoPrice;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace CPHI.Spark.WebApp.Controllers
{
   [Route("api/[controller]")]
   [ApiController]
   [Authorize]

   public class VehicleAdvertController : ControllerBase
   {
      private readonly IVehicleAdvertService vehicleAdvertService;
      private readonly IUserService userService;

      public VehicleAdvertController(IVehicleAdvertService vehicleAdvertService, IUserService userService)
      {
         this.vehicleAdvertService = vehicleAdvertService;
         this.userService = userService;
      }



      [HttpPost]
      [Route("AddComment")]
      public async Task<int> AddComment(VehicleAdvertNewCommentParams parms)
      {
         return await vehicleAdvertService.AddNewComment(parms);
      }

      [HttpPost]
      [Route("AddComments")]
      public async Task<IEnumerable<VehicleAdvertComment>> AddComments(VehicleAdvertNewCommentsParams parms)
      {
         return await vehicleAdvertService.AddNewComments(parms);
      }



      [HttpGet]
      [Route("DeleteComment")]
      public async Task DeleteComment(int commentId)
      {
         await vehicleAdvertService.DeleteComment(commentId);
         return;
      }

      [HttpPost]
      [Route("UpdateComment")]
      public async Task UpdateComment(VehicleAdvertUpdatedCommentParams parms)
      {
         await vehicleAdvertService.UpdateComment(parms);
         return;
      }


   }
}
