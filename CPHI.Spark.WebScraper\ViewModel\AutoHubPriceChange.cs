using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.WebScraper.ViewModel
{
    public class AutoHubPriceChange
    {
        // Constructor from PricingChangeMinimal
        public AutoHubPriceChange(PricingChangeMinimal pr)
        {
            Reg = pr.VehicleReg;
            Price = pr.NewPrice;
            RetailerSiteId = pr.RetailerSiteId;
            PriceChangeId = pr.PriceChangeId;
            IsAutoPriceChange = pr.IsAutoChange;
            WebsiteStockIdentifier = pr.WebsiteStockIdentifier;
        }

        // Constructor for test items
        public AutoHubPriceChange(string reg, int price)
        {
            Reg = reg;
            Price = price;
        }

        public string Reg { get; set; }
        public int Price { get; set; }
        public int RetailerSiteId { get; set; }
        public int PriceChangeId { get; set; }
        public bool IsAutoPriceChange { get; set; }
        public string WebsiteStockIdentifier { get; set; }

        // Status tracking properties
        public bool SavedOk { get; set; } = false;
        public bool PriceChanged { get; set; } = false;
        public string SaveError { get; set; } = string.Empty;

        public override string ToString()
        {
            return $"AutoHub Price Change: {Reg} -> £{Price} (Site: {RetailerSiteId}, Saved: {SavedOk})";
        }
    }
}
