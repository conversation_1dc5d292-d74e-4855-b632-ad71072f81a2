 <!-- Excel Export -->
    <div id="excelExportNew"  (mousedown)="onMouseDown('excelExportNew')" [ngClass]="{'isClicked':elementClicked==='excelExportNew'}"   (mouseleave)="onMouseUp()" 
    (mouseup)="onMouseUp()" title="Download as excel file"  (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>

<ag-grid-angular
   id="myGrid" class="ag-theme-balham"
   [rowData]="service.params.CompetitorSummary.CompetitorVehicles"
   [gridOptions]="gridOptions">
</ag-grid-angular>
