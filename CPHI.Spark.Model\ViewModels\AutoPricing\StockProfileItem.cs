﻿namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class StockProfileItem
   {
      //from stock 
      //dimensions
      public int RetailerSiteId { get; set; }
      public string RetailerSiteName { get; set; }
      public string ModelClean { get; set; }
      public string Make { get; set; }
      public string PriceBand { get; set; }
      public string AgeBand { get; set; }
      public string Drivetrain { get; set; }
      public string FuelType { get; set; }
      public string BodyType { get; set; }

      //values
      public int StockLevel { get; set; }
      public decimal PricePosition { get; set; }
      public int DaysListed { get; set; }
      public int RetailRating { get; set; }
      public int PerformanceRating { get; set; }
      public int Profit { get; set; }
      public StatsDaysListed StatsDaysListed { get; set; }
      public StatsRetailRating StatsRetailRating { get; set; }
      public StatsPerformanceRating StatsPerformanceRating { get; set; }
      public StatsVsStrategyPrice StatsVsStrategyPrice { get; set; }

      //from leaving items
      public decimal MonthlySalesRate { get; set; }
      public decimal SoldPricePosition { get; set; }
      public int SoldDaysListed { get; set; }
      public int SoldProfit { get; set; }

      //from average stock level
      public decimal AvgStockLevel { get; set; }

      //work out 
      public int StockCover { get; set; }



   }

   public class StockProfileItemMatch
   {
      public StockProfileItemMatch(StockProfileItem item)
      {
         RetailerSiteId = item.RetailerSiteId;
         RetailerSiteName = item.RetailerSiteName;
         ModelClean = item.ModelClean;
         Make = item.Make;
         PriceBand = item.PriceBand;
         AgeBand = item.AgeBand;
         Drivetrain = item.Drivetrain;
         FuelType = item.FuelType;
         BodyType = item.BodyType;

      }
      public StockProfileItemMatch(AverageVehicleAdvertModelClean item)
      {
         RetailerSiteId = item.RetailerSiteId;
         RetailerSiteName = item.RetailerSiteName;
         ModelClean = item.ModelCleanedUp;
         Make = item.Make;
         PriceBand = item.PriceBand;
         AgeBand = item.AgeBand;
         Drivetrain = item.Drivetrain;
         FuelType = item.FuelType;
         BodyType = item.BodyType;

      }
      public StockProfileItemMatch(VehicleAdvertWithRating item)
      {

         RetailerSiteId = item.RetailerSiteId;
         RetailerSiteName = item.RetailerSiteName;
         Make = item.Make;
         ModelClean = item.ModelCleanedUp;
         PriceBand = item.ValueBand;
         AgeBand = item.AgeBand;
         Drivetrain = item.Drivetrain;
         FuelType = item.FuelType;
         BodyType = item.BodyType;
      }
      public StockProfileItemMatch(LeavingVehicleModelItem item)
      {

         RetailerSiteId = item.RetailerSiteId;
         RetailerSiteName = item.RetailerSiteName;
         ModelClean = item.ModelCleanedUp;
         PriceBand = item.ValueBand;
         Make = item.Make;
         AgeBand = item.AgeBand;
         Drivetrain = item.Drivetrain;
         FuelType = item.FuelType;
         BodyType = item.BodyType;
      }
      public int RetailerSiteId { get; set; }
      public string RetailerSiteName { get; set; }
      public string ModelClean { get; set; }
      public string Make { get; set; }
      public string PriceBand { get; set; }
      public string AgeBand { get; set; }
      public string Drivetrain { get; set; }
      public string FuelType { get; set; }
      public string BodyType { get; set; }


      public override bool Equals(object obj)
      {
         if (obj is not StockProfileItemMatch other)
         {
            return false;
         }

         return RetailerSiteId == other.RetailerSiteId
             && RetailerSiteName == other.RetailerSiteName
             && ModelClean == other.ModelClean
             && PriceBand == other.PriceBand
             && AgeBand == other.AgeBand
             && Drivetrain == other.Drivetrain
             && FuelType == other.FuelType
             && BodyType == other.BodyType;
      }

      public override int GetHashCode()
      {
         unchecked
         {
            int hash = 17;

            hash = hash * 23 + RetailerSiteId.GetHashCode();
            hash = hash * 23 + (RetailerSiteName?.GetHashCode() ?? 0);
            hash = hash * 23 + (ModelClean?.GetHashCode() ?? 0);
            hash = hash * 23 + (Make?.GetHashCode() ?? 0);
            hash = hash * 23 + (PriceBand?.GetHashCode() ?? 0);
            hash = hash * 23 + (AgeBand?.GetHashCode() ?? 0);
            hash = hash * 23 + (Drivetrain?.GetHashCode() ?? 0);
            hash = hash * 23 + (FuelType?.GetHashCode() ?? 0);
            hash = hash * 23 + (BodyType?.GetHashCode() ?? 0);

            return hash;
         }
      }
   }

}