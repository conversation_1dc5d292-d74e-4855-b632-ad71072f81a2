﻿using CPHI.Spark.Model.AutoPrice;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class AverageVehicleAdvert
   {
      public int RetailerSiteId { get; set; }
      public string RetailerSiteName { get; set; }
      public string PriceBand { get; set; }
      public string FuelType{ get; set; }
      public string Model { get; set; }
      public string Make { get; set; }
      public string Drivetrain { get; set; }
      public string BodyType { get; set; }
      public string AgeBand { get; set; }
      public string RetailerSiteMakes { get; set; }

      public string ModelCleanedUp { get => AdvertStratifierService.StandardiseModelName(Model, RetailerSiteMakes, Make); }
      
      
      public decimal AvgStockLevel { get; set; }
   }

   public class AverageVehicleAdvertModelClean
   {
      public int RetailerSiteId { get; set; }
      public string RetailerSiteName { get; set; }
      public string PriceBand { get; set; }
      public string FuelType { get; set; }
      public string Make { get; set; }
      public string ModelCleanedUp { get; set; }

      public string Drivetrain { get; set; }
      public string BodyType { get; set; }
      public string AgeBand { get; set; }
      public decimal AvgStockLevel { get; set; }
      public string RetailerSiteMakes { get; set; }
   }

}