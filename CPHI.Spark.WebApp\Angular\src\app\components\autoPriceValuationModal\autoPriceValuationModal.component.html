<div class="modal-header">
   <h4 class="modal-title" id="modal-basic-title">
      Vehicle Valuation
      {{ vehicleDetailsString() }}
   </h4>

   <button type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
   </button>
</div>


<div class="modal-body" [ngClass]="constantsService.environment.customer">
   <!-- Once loaded -->
   <div *ngIf="service.valuationModalResultNew" id="modalContentContainer">
      <div id="leftScroll">

         <!-- Vehicle Details Card -->
         <div id="vehicleDetails" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'vehicleDetails')">
                  <ngb-panel id="vehicleDetails">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>Vehicle Details
                                 - {{ service.valuationModalResultNew.VehicleInformation.VehicleReg }}</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <vehicleDetailsInModal
                           [vehicleInformation]="service.valuationModalResultNew.VehicleInformation"></vehicleDetailsInModal>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Sale Details Card -->
         <div id="vehicleDetails" class="autotraderCard" *ngIf="service.valuationModalResultNew?.Valuation">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'saleDetails')">
                  <ngb-panel id="saleDetails">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>Sale Details</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <saleDetails></saleDetails>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Vehicle History Card -->
         <div id="vehicleHistory" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'vehicleHistory')">
                  <ngb-panel id="vehicleHistory">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>Vehicle History</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <vehicleHistory></vehicleHistory>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Valuation and Metrics Card -->
         <div id="valuationAndMetrics" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'valuationAndMetrics')">
                  <ngb-panel id="valuationAndMetrics">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>Trended Valuation and Metrics</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <futureValuationChart
                           [data]="service.valuationModalResultNew.TrendedValuations">
                        </futureValuationChart>
                        <div id="retailRatingAndLiveMetricsContainer">
                           <div id="retailRating">
                              <autoTraderRetailRating [params]="getRetailRatingParams()">
                              </autoTraderRetailRating>
                           </div>
                           <div id="liveMetrics">
                                        <span>
                                            Live market supply:
                                            <span
                                               *ngIf="service.valuationModalResultNew.VehicleInformation.NationalRetailSupply > 0"
                                               class="text-danger">
                                                {{
                                                  service.valuationModalResultNew.VehicleInformation.NationalRetailSupply
                                                     |
                                                     cph:'percent':0
                                               }} higher supply than normal
                                            </span>
                                            <span
                                               *ngIf="service.valuationModalResultNew.VehicleInformation.NationalRetailSupply < 0"
                                               class="text-success">
                                                {{
                                                  service.valuationModalResultNew.VehicleInformation.NationalRetailSupply
                                                     |
                                                     cph:'percent':0
                                               }} lower supply than normal
                                            </span>
                                            <span
                                               *ngIf="service.valuationModalResultNew.VehicleInformation.NationalRetailSupply === 0">None</span>
                                        </span>
                              <span>
                                            Live market demand:
                                            <span
                                               *ngIf="service.valuationModalResultNew.VehicleInformation.NationalRetailDemand > 0"
                                               class="text-success">
                                                {{
                                                  service.valuationModalResultNew.VehicleInformation.NationalRetailDemand
                                                     |
                                                     cph:'percent':0
                                               }} higher demand than normal
                                            </span>
                                            <span
                                               *ngIf="service.valuationModalResultNew.VehicleInformation.NationalRetailDemand < 0"
                                               class="text-danger">
                                                {{
                                                  service.valuationModalResultNew.VehicleInformation.NationalRetailDemand
                                                     |
                                                     cph:'percent':0
                                               }} lower demand than normal
                                            </span>
                                            <span
                                               *ngIf="service.valuationModalResultNew.VehicleInformation.NationalRetailDemand === 0">None</span>
                                        </span>
                              <span>
                                            Live market condition: {{
                                    service.valuationModalResultNew.VehicleInformation.NationalRetailMarketCondition
                                       |
                                       cph:'percent':0
                                 }}
                                        </span>
                           </div>
                        </div>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Competitor Vehicles Card -->
         <div id="sellingProspects" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'sellingProspects')">
                  <ngb-panel id="sellingProspects">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>
                                 <ng-container
                                    *ngIf="service.valuationModalResultNew?.TradePriceSetting?.IsTradePricing">Retail
                                 </ng-container>
                                 Competitor Vehicles
                                 - {{ service.valuationModalResultNew.VehicleInformation.Derivative }}
                              </h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <div style="height: 65em">
                           <competitorAnalysis [inModal]="true"
                              *ngIf="service.valuationModalResultNew.CompetitorCheckResult"></competitorAnalysis>
                        </div>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Recently Sold Card -->
         <div id="recentlySold" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'recentlySold')">
                  <ngb-panel id="recentlySold">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>{{ service.valuationModalResultNew.VehicleInformation.Model }} Sales Last 6 Months
                                 ({{ soldLast6mMessage() }})</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <recentlySoldTable [data]="service.valuationModalResultNew.RecentlySoldThisModel">
                        </recentlySoldTable>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Current stock Card -->
         <div id="currentStock" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'currentStock')">
                  <ngb-panel id="currentStock">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>Current {{ service.valuationModalResultNew.VehicleInformation.Model }} Stock <span
                                 *ngIf="!service.constantsService.environment.isSingleSiteGroup"> In Group</span>
                                 ({{ service.constantsService.pluralise(service.valuationModalResultNew.SameModelSummary.SameModelAdverts.length, 'vehicle', 'vehicles') }}
                                 )</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>


                        <!-- Table showing each vehicle -->
                        <currentStockTable
                           [data]="service.valuationModalResultNew.SameModelSummary.SameModelAdverts">
                        </currentStockTable>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Stock cover Card -->
         <div id="stockCover" class="autotraderCard">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'stockCover')">
                  <ngb-panel id="stockCover">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>
                                 {{ service.valuationModalResultNew.VehicleInformation.Model }} Stock Cover
                                 <span
                                    *ngIf="!service.constantsService.environment.isSingleSiteGroup && stockCover()>0">
                                            By Site</span>
                                 ({{ stockCover()|cph:'days':0 }})</h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <!-- Table showing cover by site -->
                        <stockAndCoverTable></stockAndCoverTable>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

         <!-- Valuation at each site Card -->
         <div id="recentlySold" class="autotraderCard" *ngIf="!constantsService.environment.isSingleSiteGroup">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'eachSiteValuation')">
                  <ngb-panel id="eachSiteValuation">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>Valuation at each site
                                 (currently showing {{ service.chosenRetailerSite?.Name }})
                              </h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                        <eachSiteValuation>
                        </eachSiteValuation>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>


         <!-- Prep Costs Card -->
         <div id="prepCosts" class="autotraderCard mb-0"
              *ngIf="service.constantsService.environment.showPrepCostsWhenValuing">
            <div class="cardInner">
               <ngb-accordion #acc="ngbAccordion" [activeIds]="openPanels"
                              (panelChange)="onPanelChange($event, 'prepCosts')">
                  <ngb-panel id="prepCosts">
                     <ng-template ngbPanelTitle>
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="factorName layerName">
                              <h3>
                                 {{ service.valuationModalResultNew.VehicleInformation.Model }} Prep Costs
                                 <span *ngIf="service.valuationModalResultNew.PrepCostItems?.length">
                                              (average {{
                                       service.valuationModalResultNew.AvgPrepCost |
                                          cph:'currency':0
                                    }})
                                            </span>
                              </h3>
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </ng-template>
                     <ng-template ngbPanelContent>
                                <span
                                   *ngIf="!service.valuationModalResultNew.PrepCostItems || service.valuationModalResultNew.PrepCostItems.length === 0">
                                    No similar vehicle prep costs found
                                </span>
                        <prepCostsChart
                           *ngIf="service.valuationModalResultNew.PrepCostItems && service.valuationModalResultNew.PrepCostItems.length > 0"
                           [data]="service.valuationModalResultNew.PrepCostItems"></prepCostsChart>
                     </ng-template>
                  </ngb-panel>
               </ngb-accordion>
            </div>
         </div>

      </div>


      <!-- ############################## -->
      <!-- Right hand side -->
      <!-- ############################## -->
      <div id="rightFixed"
           class="autotraderCard mb-0">
         <div class="cardInner">
            <div class="cardHeader">
               <div>Costing</div>
               <div>
                  <button (click)="service.valuationCosting.undo()" class="undoRedoButton">
                     <i class="fas fa-undo"></i>
                  </button>
                  <button (click)="service.valuationCosting.redo()" class="undoRedoButton">
                     <i class="fas fa-redo"></i>
                  </button>
               </div>
            </div>
            <div class="cardBody d-flex flex-column">

               <!-- The valuations bit -->
               <table class="costingsTable">
                  <tbody>
                  <tr>
                     <td colspan="3">Part-Ex valuation</td>
                     <td>
                        {{
                           service.valuationModalResultNew.ValuationPriceSet.PartExThisVehicle |
                              cph:'currency':0
                        }}
                     </td>
                  </tr>
                  <tr>
                     <td colspan="3">
                        <div
                           (click)="service.setSellingPriceTo(service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle)"
                           class="clickable">
                           Retail valuation
                        </div>
                     </td>
                     <td>
                        <div
                           (click)="service.setSellingPriceTo(service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle)"
                           class="clickable">
                           {{
                              service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle |
                                 cph:'currency':0
                           }}
                        </div>
                     </td>
                  </tr>


                  </tbody>
               </table>

               <!-- The layers of strategy price adjustment -->
               <strategyPriceBuildUpLayers [params]="buildTheBuildUpLayers()"></strategyPriceBuildUpLayers>

               <!-- The strategy price itself -->
               <table class="costingsTable">
                  <tbody>
                  <tr>
                     <td colspan="2">
                        <div
                           (click)="service.setSellingPriceTo(service.chosenVehicleLocationStrategyPriceBuild?.StrategyPrice )"
                           class="clickable">Strategy price
                           <span
                              *ngIf="!service.constantsService.environment.isSingleSiteGroup"> at {{ service.chosenRetailerSite?.Name }}</span>
                        </div>
                     </td>
                     <td>{{ service.chosenVehicleLocationStrategyPriceBuild?.StrategyPrice / service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle|cph:'percent':1 }}</td>
                     <td class="textRight">
                        <div
                           (click)="service.setSellingPriceTo(service.chosenVehicleLocationStrategyPriceBuild?.StrategyPrice)"
                           class="clickable">
                           {{
                              service.chosenVehicleLocationStrategyPriceBuild?.StrategyPrice |
                                 cph:'currency':0
                           }}
                        </div>
                     </td>
                  </tr>
                  <tr>
                     <td colspan="4">&nbsp;</td>
                  </tr>
                  </tbody>
               </table>


               <costingTable></costingTable>
            </div>
         </div>
      </div>
   </div>
   <!-- Placeholder while loading -->
   <div *ngIf="!service.valuationModalResultNew" class="d-flex justify-content-between">
      <div id="leftScroll">
         <div class="autotraderCard">
            <p class="card-text placeholder-glow" aria-hidden="true">
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
            </p>
         </div>
         <div class="autotraderCard">
            <p class="card-text placeholder-glow" aria-hidden="true">
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
            </p>
         </div>
         <div class="autotraderCard">
            <p class="card-text placeholder-glow" aria-hidden="true">
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
            </p>
         </div>
         <div class="autotraderCard">
            <p class="card-text placeholder-glow" aria-hidden="true">
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
               <span class="placeholder col-12 placeholder-lg"></span>
            </p>
         </div>

      </div>
      <div id="rightFixed" class="autotraderCard mb-0">
         <p class="card-text placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
         </p>
         <p class="card-text placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
            <span class="placeholder col-12 placeholder-lg"></span>
         </p>
      </div>
   </div>
</div>


<div class="modal-footer">
   <button type="button" class="btn btn-success" (click)="saveValuation()">Save
      Valuation
   </button>
   <button type="button" class="btn btn-primary" (click)="closeModal()">Close</button>
</div>
