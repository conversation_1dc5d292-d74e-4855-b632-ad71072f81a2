import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';

import { GetStatsDashboardParams } from 'src/app/model/GetStatsDashboardParams';
import { StatsSiteDashboard, StatsSiteDashboardDTO } from 'src/app/model/StatsSiteDashboard';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StockProfilerComponent, StockProfilerPageComponentType } from './stockProfiler.component';
import { StockProfilerTableComponent, StockProfilerTableParams } from './stockProfilerTable/stockProfilerTable.component';
import { GetStatsStockProfileItemsParams } from 'src/app/model/GetStatsStockProfileItemsParams';
import { StockProfileItem, StockProfileItemDTO } from 'src/app/model/StockProfileItem';

export enum ChosenStockProfilerLayout{
  Site='Site',
  Model='Model',
  SiteModel = 'SiteModel',
   ModelSite = 'ModelSite',
   FuelType = 'FuelType',
   BodyType = 'BodyType',
   AgeBand = 'AgeBand',
   Drivetrain = 'Drivetrain',
   Make = 'Make',
  PriceBand = 'PriceBand'
}

@Injectable({
  providedIn: 'root'
})
export class StockProfilerService implements StockProfilerTableParams {

  chosenSiteNames:Set<string> = new Set<string>();
  stockProfileItems: StockProfileItem[];

  chosenLayout: ChosenStockProfilerLayout = ChosenStockProfilerLayout.Site;
  allLifecycleStatuses: string[] = []
  defaultVehTypes: string[] = []
  chosenVehicleTypes: Set<string>;
  chosenLifecycleStatuses: Set<string>;

  serviceHasBeenInitialised: boolean = false;
  //references to components
  gridRef: StockProfilerTableComponent;
  pageRef: StockProfilerComponent;


  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public modalService: NgbModal,
    public getDataMethodsService: GetDataMethodsService,
    public cph: CphPipe
  ) {


  }


  get siteNames():string[]{
    return this.constantsService.RetailerSites.filter(x=>x.IsActive).map(x=>x.Name);
  };



  initParams(): void {


    if(!this.serviceHasBeenInitialised){
      this.chosenSiteNames = new Set<string>(this.siteNames)  //initially set to all
      this.serviceHasBeenInitialised = true;
    }

    if (!this.chosenLifecycleStatuses) {
      this.resetChosenLifecycleStatues();
    }

    if (!this.chosenVehicleTypes) {
      this.resetChosenVehTypes();
    }

  }
  async getData() {
    this.selectionsService.triggerSpinner.emit({ show: true, message: 'Loading...' });
    try {
      
      const parms: GetStatsStockProfileItemsParams = {
        ChosenRetailerSiteIds: this.constantsService.RetailerSites.filter(x => this.chosenSiteNames.has(x.Name)).map(x => x.Id),
        LifecycleStatuses: this.chosenLifecycleStatuses ? [...this.chosenLifecycleStatuses] : null,
        VehicleTypes: this.chosenVehicleTypes ? [...this.chosenVehicleTypes] : null
      };
      const res:StockProfileItemDTO[]  = await this.getDataMethodsService.getStatsStockProfileItems(parms);
      const stockProfileItems: StockProfileItem[] = res.map(x => new StockProfileItem(x));
      this.dealWithNewData(stockProfileItems);
      this.selectionsService.triggerSpinner.emit({ show: false });
    } catch (error) {
      this.constantsService.toastDanger("Error fetching data");
      this.selectionsService.triggerSpinner.emit({ show: false });
    }
  }
  
  
  //do any triggering of the various components
  dealWithNewData(data: StockProfileItem[]) {
    this.stockProfileItems=data;
    
  }

  

  dealWithFilteredItems (filteredItems: StockProfileItem[], callingComponent: StockProfilerPageComponentType) {
    //todo
  }



  resetChosenLifecycleStatues() {
    this.chosenLifecycleStatuses = new Set(this.constantsService.autopriceEnvironment.lifecycleStatusDefault); //to make them regenerate 
  }

  resetChosenVehTypes() {
    if (this.constantsService.autopriceEnvironment.defaultVehicleTypes) {
      this.chosenVehicleTypes = new Set(this.constantsService.autopriceEnvironment.defaultVehicleTypes); //to make them regenerate 
    } else {
      this.chosenVehicleTypes = null;
    }
  }
}
