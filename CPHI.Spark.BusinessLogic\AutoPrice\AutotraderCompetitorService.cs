﻿using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using log4net;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
    public class AutotraderCompetitorService
    {


      public readonly string _connString;
      public readonly AutoTraderConfig _atConfig;
      public readonly HttpClient httpClient;
      private readonly IHttpClientFactory _httpClientFactory;
      private TokenResponse _bearerToken;
      private AutoTraderCompetitorClient atCompetitorClient;
      private AutoTraderApiTokenClient atTokenClient;

      public AutotraderCompetitorService(string connString, AutoTraderConfig atConfig, IHttpClientFactory httpClientFactory)
      {
         this._connString = connString;
         this._atConfig = atConfig;
         this.httpClient = httpClientFactory.CreateClient();
         this._httpClientFactory = httpClientFactory;
      }


      public async Task<List<AutotraderAdvertIncoming>> CollectCompetitorsForAd(DealerGroupName dealerGroup, string initialUrl)
      {
         int startPrice = 0;
         
         //setup clients
         atTokenClient = new AutoTraderApiTokenClient(_httpClientFactory, _atConfig.AutotraderApiKey, _atConfig.AutotraderApiSecret, _atConfig.AutotraderBaseURL);
         atCompetitorClient = new AutoTraderCompetitorClient(_httpClientFactory, _atConfig.AutotraderApiKey, _atConfig.AutotraderApiSecret, _atConfig.AutotraderBaseURL);
         _bearerToken = await atTokenClient.GetToken();
         
         int totalRequired = await FindHowManyWeNeedForAd(initialUrl, startPrice);
         Console.WriteLine($"Need to get {totalRequired} vehicles");

         //List<AutotraderAdvertIncoming> allAds = new List<AutotraderAdvertIncoming>();

         List<AutotraderAdvertIncoming> results = new();

         while (totalRequired > results.Count)
         {

            //The main loop where we grab the next 200 ads
            //Console.WriteLine($"Got {allAdsToSave.Count} in this save period, total {allAds.Count} so far.    Elapsed time is {stopwatch.Elapsed}s.  startPrice is {startPrice}");
            _bearerToken = await atTokenClient.CheckExpiryAndRegenerate(_bearerToken);
            int remaining = totalRequired - results.Count;
            List<AutotraderAdvertIncoming> listings = await GetTenPagesOfListings(initialUrl, startPrice, remaining);

            int totalRequiredNow = await FindHowManyWeNeedForAd(initialUrl, startPrice);


            //if within 200 vehicles of end, grab those then finish
            if (listings.Count < 200)
            {
               //we must have reached the end
               Console.WriteLine($"Finishing with the final {listings.Count} vehicles");
               results.AddRange(listings);
               //allAds.AddRange(vehicles);
               //if (allAdsToSave.Count > 0)
               //{
               //   await SaveIncomingAds(isPrivate, advertiserId, dealerGroup);
               //   allAdsToSave.Clear();
               //   Console.WriteLine("Final save is now completed.");
               //}
               break;
            }

            //otherwise, more than 200, continue
            var finalPriceIn200List = listings.Last().SuppliedPrice;
            var completeRange = listings.Where(x => x.SuppliedPrice < finalPriceIn200List).ToList();
            if (completeRange.Count() > 0)
            {
               //we fully obtained at least some vehicle price range, good
               Console.WriteLine($"Top price was {finalPriceIn200List}.  Found {completeRange.Count} listings below this ranging from £{startPrice} to £{completeRange.Last().SuppliedPrice}.  ");
               //allAdsToSave.AddRange(completeRange);
               results.AddRange(completeRange);
               //Console.WriteLine($"Now we need {totalRequiredNow} including this fetch.  We just saved {completeRange.Count} so next time we should use start price of {startPrice} and shld see {totalRequiredNow - completeRange.Count}");
               //AdsCount += completeRange.Count;

               startPrice = completeRange.Last().SuppliedPrice + 1;// completeRange.Last().SuppliedPrice;
               Console.WriteLine($"Next start price is {startPrice}");
               // Check if we need to save periodically
               //if (allAdsToSave.Count > 2000)
               //{
               //   await SaveIncomingAds(isPrivate, advertiserId, dealerGroup);
               //}
            }
            else
            {
               //--------------------------------------------------------------------------------
               //None of the 200 listings is < the final price. i.e. every vehicle is the same price.   We need to stratify on odometer also
               //--------------------------------------------------------------------------------
               startPrice = listings[0].SuppliedPrice; //set the start price to the first available vehicle, and drill into that by odometer


               //--------------------------------------------------------------------------------
               //Is all we can do is to now look in the odometer ranges for this price point
               //--------------------------------------------------------------------------------

               await LoopThroughOdometerRangeForAd(initialUrl, startPrice, dealerGroup, results);

               //now we have done the odometer loop, continue to the next price
               startPrice += 1;
            }

         }

         return results;

      }


      private async Task<int> FindHowManyWeNeedForAd(string initialUrl, int startPrice)
      {
         string urlWithStartPrice = addPriceAndSortToUrl(initialUrl, startPrice);
         var count = await atCompetitorClient.GetCompetitorByAdvertiser(urlWithStartPrice, 1, _bearerToken.AccessToken,null);
         return count.totalResults;
      }


      private async Task LoopThroughOdometerRangeForAd(string existingUrl, int pricePoint, DealerGroupName dealerGroup, List<AutotraderAdvertIncoming> allAds)
      {
         //find out how many more ads we need for this price point (we already got 200)
         //string competitorLink = composeGetAdvertsForPricePointUrl(startPrice, startOdometer, advertiserId);
         Console.WriteLine($"Looping through odometer, price point is {pricePoint}.");
         int remainingToGetBasedOnOdometer = 999999;
         int weHaveGot = 0;
         int startOdometer = 0;
         while (remainingToGetBasedOnOdometer > weHaveGot)
         {
            try
            {
               int pagesOdometer = Math.Min(10, (int)Math.Ceiling(remainingToGetBasedOnOdometer / 20.0));
               List<Task<VehicleListing>> tasksOdometer = new List<Task<VehicleListing>>();
               for (int i = 1; i <= pagesOdometer; i++)
               {
                  string pageLink = addOdometerAndSortToUrl(existingUrl, startOdometer);
                  tasksOdometer.Add(atCompetitorClient.GetCompetitorByAdvertiser(pageLink, i, _bearerToken.AccessToken, null));
               }

               await Task.WhenAll(tasksOdometer);

               remainingToGetBasedOnOdometer = tasksOdometer[0].Result.totalResults; //each result will tell us how many ads are still required to get e.g. 300
               Console.WriteLine($"Remaining to get is {remainingToGetBasedOnOdometer}");
               List<AutotraderAdvertIncoming> vehicles = tasksOdometer.SelectMany(x => x.Result.results).Select(x => new AutotraderAdvertIncoming(x)).ToList();

               var finalVehicle = vehicles.Last();
               var completeRange = vehicles.Count < 200 ? vehicles : vehicles.Where(x => x.Odometer < finalVehicle.Odometer).ToList();
               var lastVehicleWeGot = completeRange.Last().Odometer;



               //allAdsToSave.AddRange(completeRange);
               allAds.AddRange(completeRange);
               //AdsCount += completeRange.Count;
               weHaveGot += completeRange.Count; //e.g. we just got 160 more

               Console.WriteLine($"Added  {completeRange.Count}, moving on to use starting Odometer of {lastVehicleWeGot + 1}.    AllAds is now {allAds.Count}");
               startOdometer = lastVehicleWeGot + 1;

               // Check if we need to save periodically
               //if (allAdsToSave.Count > 2000)
               //{
               //   await SaveIncomingAds(isPrivate, advertiserId, dealerGroup);
               //}

            }
            catch (Exception ex)
            {
               Console.WriteLine($"Exception thrown in loop through odometer: {ex.Message}");
               { }
            }
         }

         return;
      }

      private async Task<List<AutotraderAdvertIncoming>> GetTenPagesOfListings(string initialUrl, int startPrice, int remainingToGet)
      {
         int pages = Math.Min(10, (int)Math.Ceiling(remainingToGet / 20.0));
         List<Task<VehicleListing>> tasks = new();

         for (int i = 1; i <= pages; i++)
         {
            string competitorLink = addPriceAndSortToUrl(initialUrl, startPrice);
            tasks.Add(atCompetitorClient.GetCompetitorByAdvertiser(competitorLink, i, _bearerToken.AccessToken, null));
         }

         // Await all tasks, catching exceptions per-task
         VehicleListing[] results = await Task.WhenAll(
             tasks.Select(task => WrapWithCatch(task, null))
         );


         var failed = results.Where(x => x == null || x.results == null);
         if (failed.Count() > 0)
         {
            Console.WriteLine("Failed to fetch a VehicleListing page.");
         }

         // Filter out nulls (failed tasks)
         return results
             .Where(x => x != null && x.results != null)
             .SelectMany(x => x.results)
             .Select(x => new AutotraderAdvertIncoming(x))
             .ToList();
      }


      // Helper to wrap a task with exception handling
      private async Task<VehicleListing?> WrapWithCatch(Task<VehicleListing> task, ILog? logger)
      {
         try
         {
            return await task;
         }
         catch (Exception ex)
         {
            if (logger != null)
            {
               logger.Error("Failed to fetch a VehicleListing page.");
            }
            return null;
         }
      }


      private string addPriceAndSortToUrl(string url, int startPrice)
      {
         url += $"&minSuppliedPrice={startPrice}";
         url += $"&sort=suppliedPriceAsc";
         return url;
      }

      private string addOdometerAndSortToUrl(string url, int startOdometer)
      {
         url += $"&minOdometerReadingMiles={startOdometer}";
         url += $"&sort=odometerReadingMilesAsc";
         return url;
      }


   }
}
