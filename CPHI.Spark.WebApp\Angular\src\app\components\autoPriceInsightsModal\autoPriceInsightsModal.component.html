<div class="modal-header">
  <h4 class="modal-title" id="modal-basic-title">
    Vehicle Insights
  </h4>
  <div class="buttonGroup">
    <button class="btn btn-primary" [ngClass]="{ 'active': service.modalView === 'overview' }"
            (click)="changeView('overview')">
      Overview
    </button>
    <button *ngIf="service.isSmallScreen" class="btn btn-primary"
            [ngClass]="{ 'active': service.modalView === 'historicPrices' }"
            (click)="changeView('historicPrices')">
      Historic Prices
    </button>
    <button *ngIf="service.isSmallScreen" class="btn btn-primary"
            [ngClass]="{ 'active': service.modalView === 'competitorAnalysis' }"
            (click)="changeView('competitorAnalysis')">
      Competitor Analysis
    </button>
    <button *ngIf="constantsService.autopriceEnvironment.allowTestStrategy" class="btn btn-primary"
       [disabled]="service.modalItem?.HasLeft == true"
       [ngClass]="{ 'active': service.modalView === 'testStrategy' }" (click)="changeView('testStrategy')">
      Test Strategy
    </button>
    <button class="btn btn-primary" [ngClass]="{ 'active': service.modalView === 'valuation' }"
            (click)="changeView('valuation')">
      Valuation
    </button>


    <button class="btn btn-primary" [ngClass]="{ 'active': service.modalView === 'vehicleHistory' }"
            (click)="changeView('vehicleHistory')">
      Vehicle History
    </button>

    <button class="btn btn-primary" [ngClass]="{ 'active': service.modalView === 'lastSixMonths' }"
            (click)="changeView('lastSixMonths')">
      {{ service.modalItem?.AdvertDetail?.Model }} Sales Last Six Months
    </button>

      <button *ngIf="!constantsService.environment.isSingleSiteGroup" class="btn btn-primary"
              [ngClass]="{ 'active': service.modalView === 'stockInGroup' }" (click)="changeView('stockInGroup')">
         {{ service.modalItem?.AdvertDetail?.Model }} Stock In Group
      </button>

      <button *ngIf="!constantsService.environment.isSingleSiteGroup" class="btn btn-primary"
              [ngClass]="{ 'active': service.modalView === 'stockCoverBySite' }"
              (click)="changeView('stockCoverBySite')">
         {{ service.modalItem?.AdvertDetail?.Model }} Cover By Site
      </button>

    <button *ngIf="!constantsService.environment.isSingleSiteGroup" class="btn btn-primary"
            [disabled]="service.modalItem?.HasLeft == true"
            [ngClass]="{ 'active': service.modalView === 'transfer' }" (click)="changeView('transfer')">
      Site Transfer
    </button>

      <button class="btn btn-primary" [ngClass]="{ 'active': service.modalView === 'priceBoard' }"
              (click)="changeView('priceBoard')">
         Price Board
      </button>

   </div>
   <button type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
   </button>
</div>
<div class="modal-body" [ngClass]="constantsService.environment.customer">

   <!-- Overview page -->
   <div *ngIf="service.modalView === 'overview' && !service.isSmallScreen" class="dashboard-grid cols-9">


      <!-- Vehicle Details -->
      <div class="autotrader-tile grid-col-1-3 grid-row-1-7">
         <placeholder *ngIf="!service.modalItem" [tileName]="'Vehicle Details'"></placeholder>
         <vehicleDetails *ngIf="service.modalItem">
         </vehicleDetails>
      </div>

      <!-- Strategy Price Build-up -->
      <div class="autotrader-tile grid-col-3-5 grid-row-1-4">
         <placeholder *ngIf="!service.modalItem" [tileName]="'Strategy Price Build-up'"></placeholder>
         <pricingDetails [useTestStrategy]="false" *ngIf="service.modalItem"></pricingDetails>
      </div>

    <!-- Pricing Scenarios -->
    <div class="autotrader-tile grid-col-3-5 grid-row-4-7">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Pricing Scenarios'"></placeholder>
      <div [class.opacity-30]="hasLeft">
        <pricingScenarios *ngIf="service.modalItem" [hideCheck]="hasLeft"></pricingScenarios>
      </div>
    </div>

    <!-- Pricing Status and History -->
    <div class="autotrader-tile grid-col-3-5 grid-row-7-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Pricing Status and History'"></placeholder>
      <pricingHistory *ngIf="service.modalItem" (updateOptOutStatus)="service.getData()">
      </pricingHistory>
    </div>

    <!-- Profit -->
    <div class="autotrader-tile autotrader-tile grid-col-1-3 grid-row-7-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'DMS Stock Information'"></placeholder>
      <profitTile *ngIf="service.modalItem" (updateOptOutStatus)="service.getData()">
      </profitTile>
    </div>

    <!-- Historic Prices -->
    <div class="autotrader-tile grid-col-5-10 grid-row-1-6">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Historic Prices'"></placeholder>
      <pricingChart *ngIf="service.modalItem" [performanceRating]="service.modalItem?.AdvertDetail.PerfRating"
                    [performanceScore]="service.modalItem?.AdvertDetail.PerfRatingScore"></pricingChart>
    </div>

    <!-- Competitor Analysis when we are on a big screen, so it's in a tile-->
    <div class="autotrader-tile grid-col-5-9 grid-row-6-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Competitor Analysis'"></placeholder>
      <div *ngIf="service.modalItem " class="tile-inner">
        <div class="tile-header">
         <ng-container *ngIf="service.modalItem.AdvertDetail?.isTradePricing">Retail</ng-container> 
         Competitor Analysis ({{ constantsService.pluralise(service.competitorListCount, 'advert', 'adverts') }})
          <ng-container *ngIf="hasLeft">&nbsp; (Showing Current Competitor Situation)</ng-container>
        </div>
        <div class="tile-body d-flex">
          <competitorAnalysis [inModal]="true" ></competitorAnalysis>
        </div>
      </div>
    </div>

    <!-- Notes -->
    <div class="autotrader-tile grid-col-9-10 grid-row-6-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Notes'"></placeholder>
      <app-notes *ngIf="service.modalItem" [vehicleAdvertId]="service.modalItem?.AdvertDetail.AdId"></app-notes>
    </div>

    <!-- Site Transfer ONLY IF NOT SINGLESITEGROUP -->
    <!-- <div *ngIf="!constantsService.environment.isSingleSiteGroup" class="autotrader-tile grid-col-1-3 grid-row-7-10">
        <placeholder *ngIf="!service.modalItem" [tileName]="'Site Transfer'"></placeholder>
        <siteTransfer *ngIf="service.modalItem"></siteTransfer>
    </div> -->

    <!-- Search / Advert Views -->
    <!-- <div class="autotrader-tile grid-col-9-10 grid-row-1-6">
        <placeholder *ngIf="!service.modalItem" [tileName]="'Search & Advert Views'"></placeholder>
        <searchAdvertViewsChart *ngIf="service.modalItem"
            [performanceRating]="service.modalItem?.AdvertDetail.PerfRating"
            [performanceScore]="service.modalItem?.AdvertDetail.PerfRatingScore"></searchAdvertViewsChart>
    </div> -->

  </div>

  <!-- Overview page (Small screen) -->
  <div *ngIf="service.modalView === 'overview' && service.isSmallScreen" class="dashboard-grid cols-9">


      <!-- Vehicle Details -->
      <div class="autotrader-tile grid-col-1-4 grid-row-1-10">
         <placeholder *ngIf="!service.modalItem" [tileName]="'Vehicle Details'"></placeholder>
         <vehicleDetails *ngIf="service.modalItem">
         </vehicleDetails>
      </div>

      <!-- Strategy Price Build-up -->
      <div class="autotrader-tile grid-col-6-8 grid-row-1-6">
         <placeholder *ngIf="!service.modalItem" [tileName]="'Strategy Price Build-up'"></placeholder>
         <pricingDetails [useTestStrategy]="false" *ngIf="service.modalItem"></pricingDetails>
      </div>

    <!-- Pricing Scenarios -->
    <div class="autotrader-tile"
         [ngClass]="constantsService.environment.isSingleSiteGroup ? 'grid-col-4-6 grid-row-1-10' : 'grid-col-4-6 grid-row-1-6'">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Pricing Scenarios'"></placeholder>
      <div [class.opacity-30]="hasLeft">
        <pricingScenarios *ngIf="service.modalItem" [hideCheck]="hasLeft"></pricingScenarios>
      </div>
    </div>

    <!-- Pricing Status and History -->
    <div class="autotrader-tile grid-col-6-8 grid-row-6-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Pricing Status and History'"></placeholder>
      <pricingHistory *ngIf="service.modalItem" (updateOptOutStatus)="service.getData()">
      </pricingHistory>
    </div>

    <!-- Profit -->
    <div class="autotrader-tile grid-col-4-6 grid-row-6-10">
      <placeholder *ngIf="!service.modalItem && service.isSmallScreen" [tileName]="'DMS Stock Information'"></placeholder>
      <profitTile *ngIf="service.modalItem" (updateOptOutStatus)="service.getData()">
      </profitTile>
    </div>

    <!-- Notes -->
    <div class="autotrader-tile grid-col-8-10 grid-row-1-10">
      <placeholder *ngIf="!service.modalItem && service.isSmallScreen" [tileName]="'Notes'"></placeholder>
      <app-notes *ngIf="service.modalItem" [vehicleAdvertId]="service.modalItem?.AdvertDetail.AdId"></app-notes>
    </div>

    <!-- Site Transfer ONLY IF NOT SINGLESITEGROUP -->
    <div *ngIf="!constantsService.environment.isSingleSiteGroup" class="autotrader-tile grid-col-4-6 grid-row-6-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Site Transfer'"></placeholder>
      <div [class.opacity-30]="hasLeft">
        <siteTransfer *ngIf="service.modalItem"></siteTransfer>
      </div>
    </div>

    <!-- Search / Advert Views -->
    <!-- <div class="autotrader-tile grid-col-8-10 grid-row-1-6">
        <placeholder *ngIf="!service.modalItem" [tileName]="'Search & Advert Views'"></placeholder>
        <searchAdvertViewsChart *ngIf="service.modalItem"
            [performanceRating]="service.modalItem?.AdvertDetail.PerfRating"
            [performanceScore]="service.modalItem?.AdvertDetail.PerfRatingScore"></searchAdvertViewsChart>
    </div> -->
  </div>

   <!-- Historic Prices (Small screens) -->
   <div *ngIf="service.modalView === 'historicPrices' && service.isSmallScreen" class="dashboard-grid cols-9">
      <div class="autotrader-tile grid-col-1-10">
         <placeholder *ngIf="!service.modalItem" [tileName]="'Historic Prices'"></placeholder>
         <placeholder *ngIf="!service.modalItem" [tileName]="'Historic Prices'"></placeholder>
         <pricingChart *ngIf="service.modalItem" [performanceRating]="service.modalItem?.AdvertDetail.PerfRating"
                       [performanceScore]="service.modalItem?.AdvertDetail.PerfRatingScore"></pricingChart>
      </div>
   </div>

  <!-- Competitor analysis.   When it's a whole big tile, when we are on a small screen -->
  <div *ngIf="service.modalView === 'competitorAnalysis' && service.isSmallScreen" class="dashboard-grid cols-9">
    <div class="autotrader-tile grid-col-1-10">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Competitor Analysis'"></placeholder>
      <div *ngIf="service.modalItem " class="tile-inner">
        <div class="tile-header">
          Competitor Analysis ({{ constantsService.pluralise(service.competitorListCount, 'advert', 'adverts') }})
          <ng-container *ngIf="hasLeft">&nbsp; (Showing Current Competitor Situation)</ng-container>
        </div>
        <div class="tile-body d-flex">
          <competitorAnalysis [inModal]="false"></competitorAnalysis>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Strategy Values -->
  <div *ngIf="service.modalView === 'testStrategy'" class="d-flex justify-content-between h-100">

    <!-- Strategy Price Build-up -->
    <div class="autotrader-tile w-50">
      <placeholder *ngIf="!service.modalItem" [tileName]="'Test Strategy Price Build-up'"></placeholder>
      <pricingDetails [useTestStrategy]="true" *ngIf="service.modalItem"></pricingDetails>
    </div>


   </div>

   <!-- Valuations Page -->
   <div *ngIf="service.modalView === 'valuation'" class="d-flex justify-content-between h-100">
      <div id="leftSide">
         <div id="vehicleDetailsContainer" class="autotraderCard">
            <div class="cardInner">
               <div class="cardHeader">Vehicle Details</div>
               <div class="cardBody">
                  <table>
                     <tbody>
                     <tr>
                        <td>Vehicle Reg</td>
                        <td>
                           <div class="regInput">{{
                                 service.modalItem?.AdvertDetail.VehicleReg |
                                    cph:'numberPlate':0
                              }}
                           </div>
                        </td>
                     </tr>
                     <tr>
                        <td>Description</td>
                        <td>{{ service.modalItem?.AdvertDetail.Derivative }}</td>
                     </tr>
                     <tr>
                        <td>First Registered Date</td>
                        <td>{{ service.modalItem?.AdvertDetail.FirstRegisteredDate | cph:'date':0 }}</td>
                     </tr>
                     <tr>
                        <td>Mileage</td>
                        <td>
                           <div class="mileageInput">
                              {{ service.modalItem?.AdvertDetail.OdometerReading }}
                           </div>
                        </td>
                     </tr>
                     <!-- <tr>
                         <td>Condition</td>
                         <td>
                             <div class="buttonGroup m-0">
                                 <button  *ngFor="let condition of vehicleConditions" class="btn btn-primary"
                                     [ngClass]="{ 'active': condition === vehicleCondition }"
                                    >
                                     {{ condition }}
                                 </button>
                             </div>
                         </td>
                     </tr> -->
                     <!-- <tr>
                         <td>Specify Options</td>
                         <td>
                             <button class="custom-checkbox" [ngClass]="{ 'checked': specifyOptions }"
                                 (click)="specifyOptions = !specifyOptions; maybeLoadOptions()">
                                 <span *ngIf="specifyOptions">
                                     <i class="fa fa-check"></i>
                                 </span>
                             </button>
                         </td>
                     </tr> -->
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
         <!-- <div id="vehicleValuationContainer" class="autotraderCard">
             <div class="cardInner">
                 <div class="cardHeader">Live Valuation</div>
                 <div class="cardBody">
                     <p *ngIf="!valuation">
                         Please select a condition to generate a valuation for this vehicle.
                     </p>
                     <ng-container *ngIf="valuation">
                         <valuation [valuation]="valuation" [averageValuation]="averageValuation" [showAverageValuation]="specifyOptions"
                             [fullWidth]="true">
                         </valuation>
                         <div id="saveValuationContainer" class="mt-2">
                             <button *ngIf="vehicleSpecBuildForAdvert" class="btn btn-danger me-2"
                                 (click)="maybeDeleteVehicleSpecBuildForAdvert()">
                                 Delete Valuation
                             </button>
                             <button class="btn btn-success" (click)="saveVehicleSpecBuildForAdvert()">
                                 Save Valuation
                             </button>
                         </div>
                     </ng-container>
                 </div>
             </div>
         </div> -->
         <div id="vehicleValuationContainer" class="autotraderCard">
            <div class="cardInner">
               <div class="cardHeader">Valuation</div>
               <div class="cardBody">
                  <ng-container>
                     <valuation [valuation]="valuationPriceSet" [showAverageValuation]="true" [fullWidth]="true">
                     </valuation>


                  </ng-container>
               </div>
            </div>
         </div>
      </div>
      <div id="rightSide">
         <!--  -->
         <div id="optionsTableContainer" *ngIf="service.vehicleOptions" class="autotraderCard">
            <div class="cardInner">
               <div class="cardHeader">Options</div>
               <div class="cardBody d-flex flex-column">
                  <!-- <instructionRow message="Check a box to see the impact this option has on the vehicle valuation"></instructionRow> -->
                  <div id="vehicleOptionsTableContainer">
                     <!-- [data]="vehicleOptions"  -->
                     <optionsTableInsight>
                     </optionsTableInsight>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>

   <!-- Transfer page -->
   <div *ngIf="service.modalView === 'transfer'" class="d-flex flex-column h-100">
      <div id="optimiserInputs">
         <div class="me-4">
            <span>Cost per mile (£)</span>
            <input type="number" [(ngModel)]="service.costPerMile" (keyup.enter)="redrawRows()"
                   (focusout)="redrawRows()">
         </div>
         <div class="me-4">
            <span>Flat cost per move (£)</span>
            <input type="number" [(ngModel)]="service.flatCostPerMove" (keyup.enter)="redrawRows()"
                   (focusout)="redrawRows()">
         </div>
      </div>
      <locationOptimiserTable *ngIf="service.locationOptimiserAdverts" [rowData]="service.locationOptimiserAdverts">
      </locationOptimiserTable>
   </div>

   <!-- PriceBoard page -->
   <div *ngIf="service.modalView === 'priceBoard'" id="priceBoard" class="d-flex flex-column h-100">

   </div>

   <!-- vehicleHistory -->
   <div *ngIf="service.modalView === 'vehicleHistory' && service.vehicleHistoryItems" class="d-flex flex-column h-100">
      <div id="vehicleHistory" class="autotraderCard">
         <vehicleHistory></vehicleHistory>
      </div>
   </div>

   <!-- lastSixMonths -->
   <div *ngIf="service.modalView === 'lastSixMonths' && service.recentlySoldThisModel" class="d-flex flex-column h-100">
      <div id="lastSixMonths" class="autotraderCard h-100">
         <recentlySoldTable [fullHeight]="true" [data]="service.recentlySoldThisModel">
         </recentlySoldTable>
      </div>
   </div>

   <!-- stockInGroup -->
   <div *ngIf="service.modalView === 'stockInGroup' && service.sameModelAdverts" class="d-flex flex-column h-100">
      <div id="stockInGroup" class="autotraderCard h-100">
         <currentStockTable [fullHeight]="true" [data]="service.sameModelAdverts">
         </currentStockTable>
      </div>
   </div>

   <!-- stockCoverBySite -->
   <div *ngIf="service.modalView === 'stockCoverBySite' && service.stockLevelAndCover" class="d-flex flex-column h-100">
      <div id="stockCoverBySite" class="autotraderCard h-100">
         <stockAndCoverTable [fullHeight]="true"></stockAndCoverTable>
      </div>
   </div>
</div>
<div class="modal-footer">
  <ng-container *ngIf="service.allAdIds && service.allAdIds.length>0">
    <button type="button" [disabled]="!service.previousAdIdIsActive()" class="btn btn-primary"
      (click)="goToPreviousAd()">Previous Ad
    </button>
    <button type="button" [disabled]="!service.nextAdIsActive()" class="btn btn-primary" (click)="goToNextAd()">Next
      Ad
    </button>
  </ng-container>
  <button type="button" class="btn btn-primary" (click)="closeModal()">Close</button>
</div>