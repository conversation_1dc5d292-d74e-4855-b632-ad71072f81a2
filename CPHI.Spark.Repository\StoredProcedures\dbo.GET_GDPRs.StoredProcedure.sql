
--UnifiedDB - updated
CREATE OR ALTER PROCEDURE [dbo].[GET_GDPRs]
(
    @YearMonths NVARCHAR(MAX),
	@orderTypeIds NVARCHAR(MAX),
   @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @DealerGroupId INT = (SELECT DealerGroup_Id FROM People WHERE Id = @UserId)

SELECT LEFT(VALUE,4) as Year, SUBSTRING(Value,5,2) as Month INTO #yearMonths FROM STRING_SPLIT(@YearMonths,',');

WITH MainTable AS (

    SELECT 
    COALESCE(s.Description, r.Description, 'Total') AS Label,
    sa.IsManufacturer,
    COALESCE(SUM(CASE WHEN Email = 1 THEN 0.5 ELSE 0.5 END),0) AS TotalEntries,
    COALESCE(SUM(CASE WHEN Email = 1 THEN 1 ELSE 0 END),0) AS Email,
    COALESCE(SUM(CASE WHEN Phone = 1 THEN 1 ELSE 0 END),0) AS Phone,
    COALESCE(SUM(CASE WHEN Post = 1 THEN 1 ELSE 0 END),0) AS Post,
    COALESCE(SUM(CASE WHEN SMS = 1 THEN 1 ELSE 0 END),0) AS SMS
    FROM GDPRs sa 
    INNER JOIN Sites s ON s.Id=sa.Site_Id
    INNER JOIN Regions r on r.Id = s.Region_Id
	INNER JOIN #yearMonths ym on YEAR(sa.DateCaptured) = ym.YEAR AND MONTH(sa.DateCaptured) = ym.Month

    WHERE sa.Customer_Type IN (SELECT * FROM STRING_SPLIT(@orderTypeIds,','))
    AND s.DealerGroup_Id = @DealerGroupId
    GROUP BY 
    sa.IsManufacturer,
    ROLLUP (r.Description, s.Description)

)

SELECT m.*, 
s.Id AS SiteId,
r.Id AS RegionId
FROM MainTable m
LEFT JOIN Sites s ON m.Label=s.[Description]
LEFT JOIN Regions r on m.Label=r.[Description]
WHERE IsManufacturer IS NOT NULL
AND s.DealerGroup_Id = @DealerGroupId

DROP TABLE #yearMonths

END

GO
