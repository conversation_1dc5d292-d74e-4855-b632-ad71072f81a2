﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
  public class GetTodayChangesResponse
  {
    public List<PricingChangeMinimal> overdueChanges { get; set; }
    //public List<PricingChangeNew> remainingUnoverdue { get; set; }
    public List<PricingChangeMinimal> approvedChangesToAction { get; set; }
    public List<PricingChangeMinimal> totalChanges { get; set; }
  }
}
