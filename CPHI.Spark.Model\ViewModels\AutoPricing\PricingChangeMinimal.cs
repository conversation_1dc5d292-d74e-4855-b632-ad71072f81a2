﻿using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class PricingChangeMinimal
   {
      public PricingChangeMinimal() { }
      public PricingChangeMinimal(PricingChangeNew change, bool isAutoChange)
      {
         IsAutoChange = isAutoChange;

         VehicleReg = change.VehicleReg;
         Derivative = change.Derivative;
         Make = change.Make;
         WebsiteStockIdentifier = change.WebsiteStockIdentifier;
         WasPrice = change.WasPrice;
         NewPrice = change.NewPrice;
         RetailerSiteId = change.RetailerSiteId;
         RetailerName = change.RetailerName;
         PriceChangeId = change.PriceChangeId;
         DateConfirmed = change.DateConfirmed;
         IsOptedOutOnDay = change.IsOptedOutOnDay;
         ApprovedById = change.ApprovedById;
         ClickDealerFee = change.ClickDealerFee;
      }
      public string VehicleReg { get; set; }
      public string Make { get; set; }
      public bool IsAutoChange { get; set; }

      public string Derivative { get; set; }
      public string WebsiteStockIdentifier { get; set; }
      public decimal? WasPrice { get; set; }
      public int NewPrice { get; set; }
      public int RetailerSiteId { get; set; }
      public string RetailerName { get; set; }
      public int PriceChangeId { get; set; }
      public DateTime? DateConfirmed { get; set; }
      public bool IsOptedOutOnDay { get; set; }
      public int? ApprovedById { get; set; }
      public int? ClickDealerFee { get; set; }
      public decimal TotalChangeValue { get => WasPrice == 0 ? 0 : NewPrice - (WasPrice ?? 0); }
      public decimal TotalChangePercent { get => (WasPrice ?? 0) != 0 ? (decimal)TotalChangeValue / WasPrice.Value : 0; }
   }
}
