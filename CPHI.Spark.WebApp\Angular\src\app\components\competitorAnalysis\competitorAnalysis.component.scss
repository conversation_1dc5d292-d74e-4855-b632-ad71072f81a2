#competitorsTable {
   flex: 1 1 auto;
   width: 100%;
   height: 100%;
   display: flex;
   flex-direction: column;

   competitorAnalysisTable,
   competitoranalysischart {
      flex: 1;
   }
}

.plate-label {
   width: 42px;
   display: inline-block;
}

.search-container {
   padding-top: 10px;
   padding-bottom: 10px;
}

.checkbox-header {
   font-weight: 600;
   padding-bottom: 0.25em;
   padding-top: 0.25em;
}

#filterContainer {
   background-color: #f5f7f7;
   padding-top: 0.8em;
   padding-bottom: 0.8em;
   padding-left: 1em;
   border-right: 1px solid #dfdfdf;
   overflow-y: auto;
   width: 200px;
   display: flex;
   flex-direction: column;
}

.checkbox-container {
   border-bottom: 1px solid #dfdfdf;
}

.checkbox-list {
   padding-bottom: 0.5em;
   cursor: pointer;
   font-size: 1em;
}

#blobChartWrapper {
   flex: 1;
   position: relative;
}

.grid-gap-10 {
   grid-gap: 0.5em;
}

.multiselect-checkbox {
   vertical-align: middle;
}

#scrollToVehicle {
   margin-right: 1em;
}

#competitorsTable.inModal {
   height: 100%;
}

#competitorsTable {
   height: 81vh;
}