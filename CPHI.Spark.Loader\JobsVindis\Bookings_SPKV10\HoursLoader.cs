﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
    public class BookingHoursJobVindis : IJob
    {


        private static readonly ILog Logger = LogManager.GetLogger(typeof(BookingHoursJobVindis));
        private const string fileSearch = "*-export.xls";
        /*
| Column    | Example             | Sensitive |
|-----------|---------------------|-----------|
| Site      | Bedford Audi        | No        |
| Date      | 2023-03-01 00:00:00 | No        |
| Type      | Service             | No        |
| Available | 41.14               | No        |
| Booked    | 35.1                | No        |

*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();


            string[] filePaths = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot, fileSearch ));

            //check for presence of file, if so, return as already running
            if (LocksService.VindisBookingHours) { CentralLoggingService.ReportLock("BookingHoursJobVindis"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.BookingHours;
                if (age.Minutes > 120)
                {
                    PulsesService.BookingHours = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *-export.xls");
                }
                return;
            }


            //define Lists
            List<Site> dbSites;
            List<DailySiteBookingStat> dbDailySiteBookingStats;
            LocksService.VindisBookingHours = true;
            if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
            {
                System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
            }
            DateTime start = DateTime.UtcNow;

            using (var db = new CPHIDbContext())

            {
                int errorCount = 0;
                LogMessage logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 3;

                try
                {


                    dbSites = db.Sites.ToList();
                    dbDailySiteBookingStats = db.DailySiteBookingStats.ToList();

                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found

                    //go through first file
                    string filePath = filePaths[0];

                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;

                    if (File.Exists(filePath.Replace(".xls", "-p.xls")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".xls", "-p.xls")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".xls", "-p.xls");

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<DailySiteBookingStat> incomingBookingHours = new List<DailySiteBookingStat>(1000);  //preset the list size (slightly quicker than growing it each time)

                    HeadersAndRows rowsFromHelper = GetDataFromFilesService.GetExcelFileContents(newFilepath, 0, 1, null);

                    List<string> headers = rowsFromHelper.headers;

                    List<List<string>> rows = rowsFromHelper.rows;

                    int siteIndex = headers.IndexOf("Site");
                    int dateIndex = headers.IndexOf("Date");
                    int typeIndex = headers.IndexOf("Type");
                    int availableIndex = headers.IndexOf("Available");
                    int bookedIndex = headers.IndexOf("Booked");

                    foreach (var row in rows)
                    {
                        incomingCount++;

                        try
                        {

                            if (row.Count != headers.Count)
                            {
                                //something weird happened, not got enough cells for the number of headerCols, skip this record
                                logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {row.Count} cells and needed {headers.Count} file is {filePath}";
                                errorCount++;
                                continue;
                            }

                            //lookup objects required

                            string siteName = row[siteIndex];
                            DateTime date = DateTime.ParseExact(row[dateIndex], "yyyy-MM-dd HH:mm:ss", null);
                            string type = row[typeIndex];
                            decimal available = decimal.Parse(row[availableIndex]);
                            decimal booked = decimal.Parse(row[bookedIndex]);

                            int siteId = 0;

                            //branch & department
                            switch (siteName)
                            {
                                case "Bedford Audi": { siteId = 1; break; }
                                case "Bedford Volkswagen": { siteId = 6; break; }
                                case "Bury St Edmunds Skoda": { siteId = 12; break; }
                                case "Cambridge Audi": { siteId = 2; break; }
                                case "Fakenham Volkswagen": { siteId = 8; break; }
                                case "Huntingdon Audi": { siteId = 3; break; }
                                case "Huntingdon CV": { siteId = 10; break; }
                                case "Huntingdon Commercial vehicles": { siteId = 10; break; }
                                case "Huntingdon Volkswagen": { siteId = 9; break; }
                                case "Milton Keynes Seat": { siteId = 14; break; }
                                case "Northampton Audi": { siteId = 4; break; }
                                case "Northampton CV": { siteId = 11; break; }
                                case "Northampton Commercial Vehicles": { siteId = 11; break; }
                                case "Northampton Commerical Vehicles": { siteId = 11; break; } // It has been sent with this mispelling previously so kept in
                                case "Peterborough Audi": { siteId = 5; break; }
                                case "Skoda Service Centre Cambridge": { siteId = 13; break; }
                                case "Skoda Service Centre Waterbeach ": { siteId = 13; break; }
                                case "Volkswagen Service Centre Waterbeach": { siteId = 7; break; }
                                case "Volkswagen Service Centre Cambridge": { siteId = 7; break; }
                                case "Bentley Cambridge": { siteId = 16; break; }
                                case "Three10 Automotive": { siteId = 16; break; }
                                case "Three 10 Automotive": { siteId = 16; break; }
                                case "Three 10 Automotive Bentley Cambridge": { siteId = 16; break; }
                                case "Ducati Cambridge": { siteId = 15; break; }
                                default: { continue; }
                            }

                            if (siteId == 0) { continue; } //if no matching siteId found then skip this row

                            decimal jobsBooked = 0;
                            try { jobsBooked = dbDailySiteBookingStats.Find(x => x.Day == date && x.Site_Id == siteId).JobsBooked; } catch { };

                            DailySiteBookingStat existingRow = new DailySiteBookingStat();

                            existingRow = null;

                            try
                            {
                                existingRow = incomingBookingHours.Find(x => x.Day == date && x.Site_Id == siteId);
                                existingRow.Availability = existingRow.Availability += available;
                                existingRow.HoursBooked = existingRow.HoursBooked += booked;
                            }
                            catch
                            {

                                DailySiteBookingStat b = new DailySiteBookingStat(); //initialise new one

                                b.Day = date;
                                b.Availability = available;
                                b.JobsBooked = jobsBooked;
                                b.Site_Id = siteId;
                                b.HoursBooked = booked;

                                incomingBookingHours.Add(b);

                                Console.WriteLine(incomingCount);
                            }
                        }

                        catch (Exception err)
                        {
                            if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Booking Hours: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                            errorCount++;
                            continue;
                        }
                    }

                    List<DailySiteBookingStat> newItems = incomingBookingHours;
                    newCount = newItems.Count;
                    DateTime finishedInterpetFile = DateTime.UtcNow;

                    //find earliest incoming
                    DateTime earliestIncomingBooking = newItems.OrderBy(x => x.Day).First().Day;

                    //find items to remove
                    List<DailySiteBookingStat> toRemove = db.DailySiteBookingStats.Where(x => x.Day >= earliestIncomingBooking).ToList();
                    removedCount = toRemove.Count;

                    try
                    {
                        db.DailySiteBookingStats.RemoveRange(toRemove);
                        db.DailySiteBookingStats.AddRange(newItems);  //add them all in one go
                        db.SaveChanges();
                    }


                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                        errorCount++;
                    }
                    DateTime finishedUpdateDb = DateTime.UtcNow;

                    logMessage.FinishDate = DateTime.UtcNow;
                    logMessage.ProcessedCount = incomingBookingHours.Count;
                    logMessage.AddedCount = newCount;
                    logMessage.RemovedCount = removedCount;
                    logMessage.IsCompleted = true;
                    logMessage.ErrorCount = errorCount;


                    Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingBookingHours.Count} item(s) interpreted, added {newCount} new");
                    try
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xls", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xls"));
                        if (errorCount > 0)
                        {
                            //we have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            await CentralLoggingService.ReportError("BookingsHours", logMessage, true);
                            
                        }
                        else
                        {
                            //no errors so just save the log
                            logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                            logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                            logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            
                        }

                        GlobalParam dealLatestsUpdateDate = db.GlobalParams.First(x => x.Description == "bookingsUpdateDate");
                        dealLatestsUpdateDate.DateFrom = DateTime.UtcNow; // 
                        dealLatestsUpdateDate.TextValue = DateTime.UtcNow.ToLongDateString();
                        db.SaveChanges();


                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;
                        
                        await CentralLoggingService.ReportError("BookingsHours", logMessage, true);
                    }

                    //trigger cache rebuild
                    await UpdateWebAppService.Trigger("DailySiteBookingStats");
                    stopwatch.Stop();
                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();
                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                    
                    await CentralLoggingService.ReportError("BookingsHours", logMessage);

                }
                finally
                {
                    db.ChangeTracker.Clear();
                    LocksService.VindisBookingHours = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "Vindis",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }



            }
        }



    }


}
