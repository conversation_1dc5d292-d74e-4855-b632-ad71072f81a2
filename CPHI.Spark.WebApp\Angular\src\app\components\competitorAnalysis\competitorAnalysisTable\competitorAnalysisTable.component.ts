import {
   AfterViewInit,
   Component,
   EventEmitter,
   Input,
   OnChanges,
   OnInit,
   Output,
   SimpleChanges,
   ViewChild,
} from "@angular/core";
import {
   ColDef,
   GetContextMenuItemsParams,
   GridApi,
   GridOptions,
   GridReadyEvent,
   ICellRendererParams,
   RowDoubleClickedEvent,
   ValueFormatterParams,
   ValueGetterParams,
} from "ag-grid-community";
import { AutoTraderAdvertImage } from "src/app/_cellRenderers/autoTraderAdvertImage.component";
import { CphPipe } from "src/app/cph.pipe";
import { CompetitorVehicle, CompetitorVehicleDisplay } from "src/app/model/CompetitorVehicle";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ConstantsService } from "src/app/services/constants.service";
import { localeEs } from "src/environments/locale.es.js";
import { CompetitorAnalysisParams } from "../CompetitorAnalysisParams";
import { CompetitorAnalysisService } from "../competitorAnalysis.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { filter } from "rxjs/operators";
import { FilterChoice } from "../../../model/FilterChoice";
import { ExcelExportService } from "src/app/services/excelExportService";

@Component({
   selector: "competitorAnalysisTable",
   templateUrl: "./competitorAnalysisTable.component.html",
   styleUrls: ["./competitorAnalysisTable.component.scss"],
})
export class CompetitorAnalysisTableComponent implements OnInit, OnChanges {
   @Input() filters: {} | null = null;
   @Input() showExtraCols: boolean = false;

   gridOptions: GridOptions;
   gridApi: GridApi;
   elementClicked: string = null;

   constructor(
      public gridHelpersService: AGGridMethodsService,
      public constants: ConstantsService,
      public cphPipe: CphPipe,
      public service: CompetitorAnalysisService,
      private columnTypesService: ColumnTypesService,
      private excel: ExcelExportService
   ) {}

   ngOnInit(): void {
      this.gridOptions = this.setGridDefinitions();
   }

   ngOnChanges(changes: SimpleChanges): void {
      if (changes.filters && changes.filters.currentValue && this.gridApi) {
         console.log("FILTERS ", this.filters);
         this.gridApi.setFilterModel(this.filters);
         this.gridApi.onFilterChanged();
      }
   }

   setGridDefinitions(): GridOptions<any> {
      const standardColTypes = this.columnTypesService.provideColTypes([]);

      return {
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         getLocaleText: (params: any) =>
            this.constants.currentLang == "es" ? localeEs[params.key] || params.defaultValue : params.defaultValue,
         rowClassRules: { highlighted: (params) => this.isHighlighted(params) },
         getRowHeight: (params) => {
            return params.node.isRowPinned()
               ? this.gridHelpersService.getRowHeight(30)
               : this.gridHelpersService.getHeaderHeight();
         },
         defaultColDef: {
            resizable: true,
            sortable: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            autoHeight: false,
            floatingFilter: true,
         },
         columnTypes: {
            ...standardColTypes,
            price: {
               ...standardColTypes.number,
               valueGetter: (params) => params.data["AdvertisedPrice"],
               valueFormatter: (params: ValueFormatterParams) =>
                  params.node.isRowPinned() ? params.value : this.cphPipe.transform(params.value, "currency", 0),
            },
            pricePosition: {
               ...standardColTypes.number,
               valueGetter: (params) => params.data["PricePosition"],
               valueFormatter: (params: ValueFormatterParams) =>
                  params.node.isRowPinned() ? params.value : this.cphPipe.transform(params.value, "percent", 1),
            },
         },

         columnDefs: this.provideColDefs(),
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         onRowDoubleClicked: (event: RowDoubleClickedEvent) => this.goToListing(event),
         pinnedTopRowData: this.getPinnedTopRowData(),
      };
   }

   provideColDefs():ColDef[] {
      const results:ColDef[] = [
         {
            headerName: "",
            minWidth: 35,
            maxWidth: 35,
            valueGetter: (params) => this.indexColGetter(params),
            width: 35,
            type: "label",
         },
         {
            headerName: "Photo",
            maxWidth: 100,
            minWidth: 100,
            colId: "ImageURLs",
            field: "ImageURL",
            type: "special",
            cellRenderer: AutoTraderAdvertImage,
         },
         {
            headerName: "Name",
            colId: "CompetitorName",
            cellRenderer: (params) => this.nameRenderer(params),
            field: "CompetitorName",
            type: "label",
            minWidth: 250,
         },
         {
            headerName: "",
            colId: "Segment",
            cellRenderer: (params) => this.segmentRenderer(params),
            field: "Segment",
            type: "labelSetFilter",
            width: 50
         },
         { headerName: "Year", colId: "Year", field: "Year", type: "label", minWidth: 50, width: 100 },
         { headerName: "Miles", colId: "Mileage", field: "Mileage", type: "number", minWidth: 55, width:100 },
         {
            headerName: "Price",
            colId: "AdvertisedPrice",
            field: "AdvertisedPrice",
            type: "price",
            minWidth: 100,
         },
         {
            headerName: "Price Position",
            colId: "PricePosition",
            field: "PricePosition",
            type: "pricePosition",
            minWidth: 100,
            sort: "asc",
         },
         { headerName: "Distance", colId: "Distance", field: "Distance", type: "number", minWidth: 50 },
      ];

      if (this.showExtraCols) {
         results.push(
            {
               headerName: "Fuel",
               colId: "FuelType",
               field: "FuelType",
               type: "labelSetFilter",
               width:70
            },
            {
               headerName: "Body",
               colId: "BodyType",
               field: "BodyType",
               type: "labelSetFilter",
               width:100
            },
            {
               headerName: "Transmission",
               colId: "TransmissionType",
               field: "TransmissionType",
               type: "labelSetFilter",
               width: 150,
            },
            {
               headerName: "Doors",
               colId: "Doors",
               field: "Doors",
               type: "labelSetFilter",
               width: 100,
            },
            {
               headerName: "Trim",
               colId: "Trim",
               field: "Trim",
               type: "labelSetFilter",
               width: 150,
            },
            {
               headerName: "Town",
               colId: "Town",
               field: "Town",
               type: "labelSetFilter",
               width: 150,
            },
            {
               headerName: "Region",
               colId: "Region",
               field: "Region",
               type: "labelSetFilter",
               width: 150,
            },
            {
               headerName: "Postcode",
               colId: "Postcode",
               field: "Postcode",
               type: "labelSetFilter",
               width: 100,
            },
         )
      }
         return results;
      
   }

   indexColGetter(params: ValueGetterParams<any>): any {
      if (params.node.isRowPinned()) {
         return "";
      }
      return params.node.rowIndex + 1;
   }

   nameRenderer(params: ICellRendererParams) {
      const segment = params.data.Segment;
      return params.value ?? "Private seller";
   }

   segmentRenderer(params: ValueGetterParams) {
      const segment = params.data.Segment;
      //    if(segment=='Our Vehicle'){return null;}
      return !!segment && segment.length > 0 ? segment[0] : null;
   }

   dealWithNewData(params: CompetitorAnalysisParams) {
      if (this.gridApi) {
         this.gridApi.setRowData(params.CompetitorSummary.CompetitorVehicles);
         this.gridApi.setPinnedTopRowData(this.getPinnedTopRowData());
      }
   }

   isHighlighted(params: any): boolean {
      const isOurVehicle = params.data?.IsOurVehicle;
      return isOurVehicle;
   }

   getImage(params: ICellRendererParams) {
      const row: CompetitorVehicle = params.data;

      if (!row || !row?.ImageURL) return "";
      return `<img style="height: 50px; width: 100%;" src=${row.ImageURL} />`;
   }

   onGridReady(event: GridReadyEvent) {
      this.gridApi = event.api;
      this.service.tableRef = this;
      setTimeout(() => {
         this.gridApi.sizeColumnsToFit();
      }, 200);
   }

   goToListing(event: any) {
      const websiteSearchIdentifier: string = event.data.WebsiteSearchIdentifier;
      const url: string = this.constants.buildAdUrl(websiteSearchIdentifier, event.data.VehicleType);
      window.open(url, "_blank").focus();
   }

   getContextMenuItems(params: GetContextMenuItemsParams<any, any>) {
      const clickedItem: CompetitorVehicle = params.node.data;

      var menuOptions = [
         "copy",
         "copyWithHeaders",
         "separator",
         {
            icon: "🔗",
            name: "View advert (opens in new tab)",
            cssClasses: ["bold"],
            action: () => {
               this.goToListing(params.node);
            },
         },
      ];

      if (this.service.params.ParentType == "valuationModal") {
         menuOptions.push(
            {
               icon: "£",
               cssClasses: [],
               name: "Beat this price",
               action: () => {
                  this.service.params.VehicleValuationService.setSellingPriceTo(
                     (clickedItem.AdvertisedPrice as number) - 1
                  );
               },
            },
            {
               icon: "%",
               cssClasses: [],
               name: "Beat this price position",
               action: () => {
                  const newPrice: number =
                     this.service.params.VehicleValuationService.valuationModalResultNew.ValuationPriceSet
                        .RetailThisVehicle *
                     ((clickedItem.PricePosition as number) - 0.0001);
                  this.service.params.VehicleValuationService.setSellingPriceTo(newPrice);
               },
            }
         );
      }

      return menuOptions;
   }

   getPinnedTopRowData() {
      let ourAdDetail = this.buildUpOurAdDetail();
      //console.log(ourAdDetail);
      return [ourAdDetail];
   }

   public buildUpOurAdDetail(): CompetitorVehicleDisplay {
      //return this.service.params.CompetitorSummary.CompetitorVehicles.find(x=>x.IsOurVehicle);
      return {
         ImageURL: "",
         CompetitorName: "This advert",
         Year: this.cphPipe.transform(this.service.params.FirstRegisteredDate, "year", 0),
         Mileage: this.service.params.OdometerReading,
         AdvertisedPrice: `${this.cphPipe.transform(this.service.params.AdvertisedPrice, "currency", 0)} (${
            this.service.params.CompetitorSummary.PriceRank
         }/${this.service.params.CompetitorSummary.CompetitorVehicleCount})`,
         PricePosition: `${this.cphPipe.transform(this.service.params.PricePosition, "percent", 1)} (${
            this.service.params.CompetitorSummary.ValueRank
         }/${this.service.params.CompetitorSummary.CompetitorVehicleCount})`,
         Distance: 0,
      } as CompetitorVehicleDisplay;
   }

   filteredVehicles(competitorVehicles: CompetitorVehicle[]) {
      return competitorVehicles;
   }

   protected readonly filter = filter;

   onMouseDown(elementId: string): void {
      this.elementClicked = elementId;
   }

   onMouseUp(): void {
      this.elementClicked = null;
   }

   excelExport() {
      let tableModel = this.gridApi.getModel();
      this.excel.createSheetObject(tableModel, "Competitor Analysis", 1, 1);
   }
}
