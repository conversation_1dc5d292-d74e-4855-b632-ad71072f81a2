﻿using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Data;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
    public partial class TechCountVindisJob : IJob
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(TechCountVindisJob));

        private List<SiteTechCount> newEntries;

        private static DateTime LastLoggedNoFiles = DateTime.UtcNow;

        // Define Lists
        private int errorCount;

        private string[] allMatchingFiles;
        private string fileToProcess;

        private int incomingProcessCount;

        // Set the max number of entries to be removed from db
        // Attempting to remove more than this number will trigger an error.
        private const int removalLimit = 700;

        // Prevent logging more than a set number of errors.
        private const int maxErrorLimit = 30;

        // Minutes to wait before displaying NoFilesFoundMsg
        private const int maxDelayMinutes = 10;

        public CPHIDbContext db;

        private LogMessage logMessage;

        DataRowCollection allRows;
        private string[] headers;
        private string newFilepath;

        private Dictionary<string, int> headerColIndex;
        private List<Site> dbSites;
        private const string fileSearch = "*TechHeadsAndTargets*";
        /*
not sensitive
*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            //---------------------------------------------------------------------------------------------------
            logMessage = new LogMessage();
            logMessage.DealerGroup_Id = 3;

            // If validation fails, do not continue.
            if (!FileValidation()) { return; }

            // Check for presence of lock, if so, return as already running
            if (LocksService.VindisTechCounts) { return; }

            // Create lock to prevent other instances running
            LocksService.VindisTechCounts = true;
            if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
            {
                System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
            }
            DateTime start = DateTime.UtcNow;

            using (var db = new CPHIDbContext())

            {



                CreateNewFileFoundMessage(logMessage, fileToProcess);
                newEntries = new List<SiteTechCount>();

                try
                {
                    dbSites = await db.Sites.ToListAsync();
                    if (CheckIfFileAlreadyProcessing()) { FileAlreadyProcessingMsg(); }
                    SetNewFileNameAndPath();
                    GetDataFromFile();
                    DateTime finishedInterpetFile = DateTime.UtcNow;
                    DeleteOldData(db);
                    AddNewItems(db);
                    DateTime finishedUpdateDb = DateTime.UtcNow;

                    try
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));

                        if (errorCount > 0)
                        {
                            // We have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            logMessage.DealerGroup_Id = 3;
                            db.LogMessages.Add(logMessage);
                            errorCount--;
                            
                            await CentralLoggingService.ReportError("TechCounts", logMessage, true);
                        }
                        else
                        {
                            // Completed succesfully, save log and remove lock
                            
                            logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                            logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                            logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                            logMessage.DealerGroup_Id = 3;
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();

                        }
                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;
                        logMessage.DealerGroup_Id = 3;
                        // CentralLogging.ReportError("TechCounts", logMessage);
                        db.LogMessages.Add(logMessage);
                        
                    }
                    stopwatch.Stop();
                }

                //Global error catcher
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();

                    await EmailerService.SendErrorMail("Vindis tech count loader failed", err.StackTrace);
                    await GeneralFailureMsg(err);
                }

                finally
                {
                    db.ChangeTracker.Clear();
                    LocksService.VindisTechCounts = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "Vindis",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };

                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }

            }
        }






        private void DeleteOldData(CPHIDbContext db)
        {

            try
            {
                DateTime thisDate = newEntries.First().MonthStart;
                List<SiteTechCount> toRemove = db.SiteTechCounts.Where(x => x.MonthStart.Date == thisDate.Date).ToList();
                int remCount = toRemove.Count();

                // If too many are removed, this will throw a general exception.
                if (remCount > removalLimit) throw new Exception("Too many removed!");

                db.SiteTechCounts.RemoveRange(toRemove);  // Add them all in one go
                Logger.Info($@"[{DateTime.UtcNow}] | TechCount | Deleted {remCount} items to the database for month starting {thisDate.Date}.");

            }
            catch (Exception err)
            {
                logMessage.FailNotes += $"Failed addingNewStocks range {err}";
                errorCount++;
            }

        }

        private void AddNewItems(CPHIDbContext db)
        {

            try
            {
                int newCount = newEntries.Count();
                DateTime incomingDate = newEntries.First().MonthStart;

                db.SiteTechCounts.AddRange(newEntries);
                db.SaveChanges();

                Logger.Info($@"[{DateTime.UtcNow}] | TechCount | Added {newCount} items to the database for month starting {incomingDate}.");
            }
            catch (Exception err)
            {
                logMessage.FailNotes += $"Failed addingNewStocks range {err}";
                errorCount++;
            }

        }

        private bool FileValidation()
        {
            // Getting files from folder
            allMatchingFiles = Directory.GetFiles(ConfigService.incomingRoot, fileSearch );

            // No files found.
            if (allMatchingFiles.Length == 0) { CreateNoFilesFoundMessage(); return false; }

            // Check for presence of file, if so, return as already running
            if (LocksService.VindisTechCounts) { CentralLoggingService.ReportLock("TechCount"); return false; }

            // Data valid, continue
            fileToProcess = allMatchingFiles[0];

            // Try opening the file, if fails, return (common problem is loader trying to open file whilst scraper is saving it).
            try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
            catch (IOException) { return false; }

            // Create lock to prevent other instances running
            LocksService.Stocks = true;

            return true;
        }

        private void CreateNoFilesFoundMessage()
        {
            TimeSpan age = DateTime.UtcNow - PulsesService.Stocks;

            if (age.Minutes > maxDelayMinutes)
            {
                PulsesService.Stocks = DateTime.UtcNow;
                Logger.Info($@"[{DateTime.UtcNow}] | TechCount | No files found matching pattern *TechHeadsAndTargets*");
            }
        }

        public void CreateNewFileFoundMessage(LogMessage logMessage, string fileToProcess)
        {
            logMessage.SourceDate = DateTime.UtcNow;
            logMessage.Job = this.GetType().Name;

            Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");   //update logger 
        }

        public void GetDataFromFile()
        {
            //SetHeaderRow();
            allRows = GetDataFromFilesService.GetRowsExcelNew(newFilepath);

            var x = allRows[0];
            var y = allRows[1];

            foreach (DataRow row in allRows)
            {

                incomingProcessCount++;

                // Skip these first rwo rows
                if (row == allRows[0] || row == allRows[1]) { continue; }

                // Header row
                if (row == allRows[2])
                {
                    headers = (from o in row.ItemArray
                               select o.ToString().ToUpper()).ToArray();

                    SetHeaderColIndexDict();
                    continue;
                }

                try
                {
                    if (string.IsNullOrEmpty(row.ItemArray[0].ToString())) { continue; } // skip empties

                    var newRow = new SiteTechCount();

                    newRow.MonthStart = (DateTime)row.ItemArray[headerColIndex["MONTH START"]];
                    newRow.TechHeadCount = (decimal)Convert.ToSingle(row.ItemArray[headerColIndex["TECH HEADCOUNT (FTE)"]]);
                    newRow.TechTargetPerDay = (decimal)Convert.ToSingle(row.ItemArray[headerColIndex["TECH DAILY TARGET (£)"]]);

                    int siteId = GetSiteId(row.ItemArray[headerColIndex["SITE"]].ToString());
                    Site site = dbSites.First(st => st.Id == siteId);

                    newRow.Site = site;

                    newEntries.Add(newRow);
                }

                catch (Exception err)
                {
                    if (errorCount < maxErrorLimit) logMessage.FailNotes += $" failed on adding item, Item: {incomingProcessCount} {err}";
                    errorCount++;
                    continue;
                }

            }

        }

        private int GetSiteId(string siteName)
        {
            int siteId = 0;

            switch (siteName)
            {
                case "Audi Northampton": { siteId = 4; break; }
                case "VW Bedford": { siteId = 6; break; }
                case "VW Cambridge": { siteId = 7; break; }
                case "Audi Peterborough": { siteId = 5; break; }
                case "Bentley Cambridge": { siteId = 16; break; }
                case "Three10": { siteId = 16; break; }
                case "Three 10": { siteId = 16; break; }
                case "Three10 Automotive": { siteId = 16; break; }
                case "Three 10 Automotive": { siteId = 16; break; }
	            case "Three 10 Automotive Bentley Cambridge": { siteId = 16; break; }
                case "Ducati Cambridge": { siteId = 15; break; }
                case "Van Centre Northampton": { siteId = 11; break; }
                case "VW Huntingdon": { siteId = 9; break; }
                case "VW Fakenham": { siteId = 8; break; }
                case "VW CV Northampton": { siteId = 11; break; }
                case "VW CV Huntingdon": { siteId = 10; break; }
                case "Audi Cambridge": { siteId = 2; break; }
                case "Audi Huntingdon": { siteId = 3; break; }
                case "Audi Bedford": { siteId = 1; break; }
                case "Skoda Cambridge": { siteId = 13; break; }
                case "Skoda Bury": { siteId = 12; break; }
                case "SEAT Milton Keynes": { siteId = 14; break; }
                case "AutoNow Cambridge": { siteId = 18; break; }
                case "AutoNow Bury": { siteId = 17; break; }
                case "AutoNow Fakenham": { siteId = 19; break; }
                case "AutoNow Sawston": { siteId = 20; break; }
                case "Trade Parts Specialist": { siteId = 21; break; }
                case "Group Fleet": { siteId = 22; break; }
                case "Group": { siteId = 23; break; }
                case "Epic Parts": { siteId = 25; break; }
            }

            return siteId;
        }

        private void SetHeaderColIndexDict()
        {

            headerColIndex = new Dictionary<string, int>(){
                        {"SITE", Array.IndexOf(headers, "SITE")},
                        {"MONTH START", Array.IndexOf(headers, "MONTH START")},
                        {"TECH HEADCOUNT (FTE)", Array.IndexOf(headers, "TECH HEADCOUNT (FTE)")},
                        {"TECH DAILY TARGET (£)", Array.IndexOf(headers, "TECH DAILY TARGET (£)")},
                    };
        }

        private async Task GeneralFailureMsg(Exception err)
        {
            logMessage.FailNotes = logMessage.FailNotes + $"General failure " + err.ToString();
            errorCount++;
            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;

            await CentralLoggingService.ReportError("TechCounts", logMessage);
            LocksService.Stocks = false;
        }

        private void SetNewFileNameAndPath()
        {
            File.Move(fileToProcess, fileToProcess.Replace(".xlsx", "-p.xlsx")); // append _processing to the file to prevent any other instances also processing these files
            newFilepath = fileToProcess.Replace(".xlsx", "-p.xlsx");
        }

        private bool CheckIfFileAlreadyProcessing()
        {
            return File.Exists(fileToProcess.Replace(".xlsx", "-p.xlsx"));
        }

        private void FileAlreadyProcessingMsg()
        {
            Logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
            logMessage.FailNotes += "Processing file already found.";
        }








    }


}
