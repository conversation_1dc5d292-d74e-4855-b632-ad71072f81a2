﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("RetailerSites", Schema = "autoprice")]
   public class RetailerSite
   {
      public int Id { get; set; }

      //FKs
      public int Site_Id { get; set; }
      [ForeignKey("Site_Id")]
      public Site Site { get; set; }

      public int DealerGroup_Id { get; set; }
      [ForeignKey("DealerGroup_Id")]
      public DealerGroup DealerGroup { get; set; }

      //which strategy selection rule set this retailer is currently using.   This is our new simpler approach, where every retailerSite has exactly 1 of these
      public int? StrategySelectionRuleSet_Id { get; set; }
      [ForeignKey("StrategySelectionRuleSet_Id")]
      public StrategySelectionRuleSet StrategySelectionRuleSet { get; set; }

      public int? BuyingStrategySelectionRuleSet_Id { get; set; }
      [ForeignKey("BuyingStrategySelectionRuleSet_Id")]
      public StrategySelectionRuleSet BuyingStrategySelectionRuleSet { get; set; }

      public int? BuyingStrategySelectionRuleSet2_Id { get; set; }  //not sure if we use this, to check
      [ForeignKey("BuyingStrategySelectionRuleSet2_Id")]
      public StrategySelectionRuleSet BuyingStrategySelectionRuleSet2 { get; set; }

      public int? TestStrategySelectionRuleSet_Id { get; set; }
      [ForeignKey("TestStrategySelectionRuleSet_Id")]
      public StrategySelectionRuleSet TestStrategySelectionRuleSet { get; set; }


      public ICollection<BuyingCostsSet> BuyingCostsSets { get; set; }


      [Column(TypeName = "varchar(30)")]
      public string Name { get; set; }
      public int RetailerId { get; set; }
      public bool IsActive { get; set; }

      [Column(TypeName = "varchar(10)")]
      public string Postcode { get; set; }

      [Column(TypeName = "nvarchar(500)")]
      public string Makes { get; set; }


      [Column(TypeName = "varchar(30)")]
      public string FakeName { get; set; }


      public int? ClickDealerFee { get; set; }  // for updating prices


      //.................................................................................
      //AutoPrice settings maintainable by users
      //.................................................................................
      #region MaintainableByUsers

      //Updating Prices
      public bool UpdatePricesAutomatically { get; set; }
      public bool UpdatePricesMon { get; set; }
      public bool UpdatePricesTue { get; set; }
      public bool UpdatePricesWed { get; set; }
      public bool UpdatePricesThu { get; set; }
      public bool UpdatePricesFri { get; set; }
      public bool UpdatePricesPubHolidays { get; set; }
      public bool UpdatePricesSat { get; set; }
      public bool UpdatePricesSun { get; set; }
      public int WhenToActionChangesEachDay { get; set; }


      //OptOuts
      public int MaximumOptOutDays { get; set; }

      //Buying opportunities
      public int LocalBargainThreshold { get; set; }
      public int LocalBargainsSearchRadius { get; set; } = 50;
      public int LocalBargainsMinRetailRating { get; set; } = 0;

      //Location Optimiser
      public decimal LocationMovePoundPerMile { get; set; }
      public decimal LocationMoveFixedCostPerMove { get; set; }

      //Competitor Searching
      public int CompetitorPlateRange { get; set; }

      //Buying targets
      public int TargetMargin { get; set; }
      public decimal TargetAdditionalMech { get; set; }
      public decimal TargetPaintPrep { get; set; }
      public decimal TargetAuctionFee { get; set; }
      public decimal TargetDelivery { get; set; }
      public decimal TargetWarrantyFee { get; set; }
      public decimal TargetOtherCost { get; set; }

      #endregion



      //.................................................................................
      //Properties for configuration that are not maintainable by users
      //.................................................................................
      #region NotMaintainableByUsers

      public decimal OverUnderThreshold { get; set; }
      public bool OverUnderIsPercent { get; set; }
      public decimal VeryThreshold { get; set; }
      public bool VeryThresholdIsPercent { get; set; }



      public bool AllowTestStrategy { get; set; }
      public bool SeparateBuyingStrategy { get; set; }
      public bool SeparateBuyingStrategy2 { get; set; } //possible deletion 

      public bool SkipBulkValuation { get; set; } //only V12 currently
      public bool AllowPriceScenarios { get; set; } //the v12 thing

      //Defining whether a price changes is 'small'
      public int MinimumAutoPriceDecrease { get; set; }
      public int MinimumAutoPriceIncrease { get; set; }
      [Column(TypeName = "decimal(9, 3)")]
      public decimal MinimumAutoPricePercentDecrease { get; set; }
      [Column(TypeName = "decimal(9, 3)")]
      public decimal MinimumAutoPricePercentIncrease { get; set; }

      [MaxLength(50)]
      public string SortPriceChangesReportBy { get; set; }  //e.g. daysListedAsc or daysListedDesc (KCSOfSurrey like desc)

      [MaxLength(20)]
      public string RetailerType { get; set; } //e.g. Independent, Franchise, Dealer etc.  For front end use
      public List<FixedCostWarranty> FixedCostWarranties { get; set; }
      public TradePriceSetting TradePriceSetting { get; set; }



      //for site specific vsStrategy definition

      //report toggle defaults
      public bool ShowNewVehicles { get; set; }
      public bool IncludeUnPublishedAds { get; set; }

      public string LifecycleStatusDefaults { get; set; }  //e.g. FORECOURT, SALE_IN_PROGRESS etc.  which ones we should highlight by default.   


      public string VehicleTypes { get; set; }  //array of all the possible vehicle types (vehicle type from DMS records)
      [Column(TypeName = "nvarchar(400)")]
      public string DefaultVehicleTypes { get; set; } //array of the default chosen vehicle types



      public int MorningReportDISUpTo { get; set; }   //limits vehicles based on days in stock.  Pentagon requested this.


      public bool StockReport_ShowDmsSellingPrice { get; set; }   //Vindis requested this
      public bool StockReport_ShowVsDmsSellingPrice { get; set; }  //Vindis requested this
      public bool StockReport_ShowPhysicalLocation { get; set; } //Vindis requested this
      public string DaysMeasure { get; set; } //DaysListed for most.  Pentagon like DaysInStock.  Influences which cols are shown by default in front end
      public bool DefaultToDaysInStock { get; set; } //determines the col that appears by default on the main report.   Think we can kill this and just use DaysMeasure == DaysInStock

      public bool LeavingVehicles_IncludeUnPublished { get; set; }
      public bool TrackLeavingVehicles { get; set; }
      #endregion
      public DateTime DateDailyAdvertDataFirstAvailable { get; set; }


      //Dead ones?
      public int AdminFee { get; set; }  //possibly dead



   }
}
